<?php
/**
 * PHP 环境信息检查脚本
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>PHP 环境信息检查</h1>";

echo "<h2>1. PHP 版本信息</h2>";
echo "PHP 版本: " . PHP_VERSION . "<br>";
echo "PHP 主版本: " . PHP_MAJOR_VERSION . "<br>";
echo "PHP 次版本: " . PHP_MINOR_VERSION . "<br>";
echo "PHP 发布版本: " . PHP_RELEASE_VERSION . "<br>";

echo "<h2>2. 关键函数支持检查</h2>";
echo "str_ends_with 函数存在: " . (function_exists('str_ends_with') ? '<span style="color: green;">是</span>' : '<span style="color: red;">否</span>') . "<br>";
echo "str_starts_with 函数存在: " . (function_exists('str_starts_with') ? '<span style="color: green;">是</span>' : '<span style="color: red;">否</span>') . "<br>";
echo "str_contains 函数存在: " . (function_exists('str_contains') ? '<span style="color: green;">是</span>' : '<span style="color: red;">否</span>') . "<br>";

// 测试 str_ends_with 函数
if (function_exists('str_ends_with')) {
    echo "<h3>str_ends_with 函数测试:</h3>";
    $test_string = "AiTaskController.php";
    $result1 = str_ends_with($test_string, '.php');
    $result2 = str_ends_with("AiTaskController", '.php');
    echo "str_ends_with('AiTaskController.php', '.php'): " . ($result1 ? 'true' : 'false') . "<br>";
    echo "str_ends_with('AiTaskController', '.php'): " . ($result2 ? 'true' : 'false') . "<br>";
}

echo "<h2>3. 文件系统检查</h2>";
$pyApiDir = __DIR__ . '/php/api/app/Http/Controllers/PyApi';
echo "PyApi 目录路径: " . $pyApiDir . "<br>";
echo "PyApi 目录存在: " . (is_dir($pyApiDir) ? '<span style="color: green;">是</span>' : '<span style="color: red;">否</span>') . "<br>";

if (is_dir($pyApiDir)) {
    $files = scandir($pyApiDir);
    $phpFiles = array_filter($files, function($file) {
        return pathinfo($file, PATHINFO_EXTENSION) === 'php';
    });
    echo "PHP 控制器文件数量: " . count($phpFiles) . "<br>";
    echo "控制器文件列表:<br>";
    foreach ($phpFiles as $file) {
        echo "- " . $file . "<br>";
    }
}

echo "<h2>4. Laravel/Lumen 环境检查</h2>";
try {
    // 检查 Composer 自动加载
    if (file_exists(__DIR__ . '/php/api/vendor/autoload.php')) {
        echo "Composer 自动加载文件: <span style='color: green;'>存在</span><br>";
        require_once __DIR__ . '/php/api/vendor/autoload.php';
        
        // 检查 Lumen 应用
        if (file_exists(__DIR__ . '/php/api/bootstrap/app.php')) {
            echo "Lumen 应用文件: <span style='color: green;'>存在</span><br>";
            
            // 尝试创建应用实例
            $app = require_once __DIR__ . '/php/api/bootstrap/app.php';
            echo "Lumen 应用实例: <span style='color: green;'>创建成功</span><br>";
            echo "应用版本: " . $app->version() . "<br>";
            
            // 检查控制器类是否存在
            if (class_exists('App\Http\Controllers\PyApiDocumentController')) {
                echo "PyApiDocumentController 类: <span style='color: green;'>存在</span><br>";
                
                // 尝试实例化控制器
                $controller = new \App\Http\Controllers\PyApiDocumentController();
                echo "控制器实例化: <span style='color: green;'>成功</span><br>";
                
                // 检查方法是否存在
                $methods = ['getControllerList', 'getControllerData', 'parseControllerList'];
                foreach ($methods as $method) {
                    if (method_exists($controller, $method)) {
                        echo "方法 {$method}: <span style='color: green;'>存在</span><br>";
                    } else {
                        echo "方法 {$method}: <span style='color: red;'>不存在</span><br>";
                    }
                }
                
            } else {
                echo "PyApiDocumentController 类: <span style='color: red;'>不存在</span><br>";
            }
            
        } else {
            echo "Lumen 应用文件: <span style='color: red;'>不存在</span><br>";
        }
    } else {
        echo "Composer 自动加载文件: <span style='color: red;'>不存在</span><br>";
    }
} catch (\Exception $e) {
    echo "Laravel/Lumen 检查异常: <span style='color: red;'>" . $e->getMessage() . "</span><br>";
    echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "<br>";
}

echo "<h2>5. 内存和性能信息</h2>";
echo "内存限制: " . ini_get('memory_limit') . "<br>";
echo "最大执行时间: " . ini_get('max_execution_time') . " 秒<br>";
echo "当前内存使用: " . round(memory_get_usage() / 1024 / 1024, 2) . " MB<br>";
echo "峰值内存使用: " . round(memory_get_peak_usage() / 1024 / 1024, 2) . " MB<br>";

echo "<h2>6. 扩展信息</h2>";
$required_extensions = ['json', 'mbstring', 'openssl', 'pdo', 'tokenizer', 'xml'];
foreach ($required_extensions as $ext) {
    $loaded = extension_loaded($ext);
    echo "扩展 {$ext}: " . ($loaded ? '<span style="color: green;">已加载</span>' : '<span style="color: red;">未加载</span>') . "<br>";
}

echo "<hr>";
echo "<h2>完整 PHP 信息</h2>";
echo "<div style='max-height: 500px; overflow-y: auto; border: 1px solid #ccc; padding: 10px;'>";
phpinfo();
echo "</div>";

echo "<hr>";
echo "<h2>总结</h2>";
echo "<p>如果 PHP 版本确实是 8.0+，那么 500 错误可能是由其他原因引起的：</p>";
echo "<ul>";
echo "<li>类文件加载问题</li>";
echo "<li>依赖注入问题</li>";
echo "<li>数据库连接问题</li>";
echo "<li>权限问题</li>";
echo "<li>其他运行时错误</li>";
echo "</ul>";

?>
