<?php

namespace App\Jobs;

use App\Models\AiGenerationTask;
use App\Models\ProjectStoryboard;
use App\Services\PyApi\ImageService;
use App\Services\PyApi\WebSocketEventService;
use App\Services\PyApi\PointsService;
use Illuminate\Support\Facades\Http;
use App\Enums\ApiCodeEnum;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;

use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Carbon\Carbon;

/**
 * 异步处理分镜图片生成任务
 * 实现图表中的完整 WebSocket 进度推送流程
 */
class ProcessStoryboardImageGeneration implements ShouldQueue
{
    use InteractsWithQueue, Queueable, SerializesModels;

    protected $taskId;
    protected $userId;
    protected $storyboardIds;
    protected $generationParams;
    protected $mode;

    /**
     * 任务超时时间（秒）
     */
    public $timeout = 600; // 10分钟

    /**
     * 最大重试次数
     */
    public $tries = 3;

    public function __construct(string $taskId, int $userId, array $storyboardIds, array $generationParams, string $mode = 'batch')
    {
        $this->taskId = $taskId;
        $this->userId = $userId;
        $this->storyboardIds = $storyboardIds;
        $this->generationParams = $generationParams;
        $this->mode = $mode;
    }

    /**
     * 执行任务
     */
    public function handle()
    {
        $webSocketEventService = app(WebSocketEventService::class);
        $imageService = app(ImageService::class);
        $pointsService = app(PointsService::class);

        try {
            DB::beginTransaction();

            // 推送任务开始进度
            $webSocketEventService->pushAiGenerationProgress(
                $this->taskId,
                $this->userId,
                10,
                "开始分镜图片生成任务"
            );

            // 获取分镜列表
            $storyboards = ProjectStoryboard::whereIn('id', $this->storyboardIds)
                ->where('status', '!=', ProjectStoryboard::STATUS_GENERATING)
                ->get();

            if ($storyboards->isEmpty()) {
                throw new \Exception('没有找到需要生成的分镜');
            }

            $totalStoryboards = $storyboards->count();
            $processedCount = 0;
            $successCount = 0;
            $failedCount = 0;
            $totalCost = 0;

            // 推送分析进度
            $webSocketEventService->pushAiGenerationProgress(
                $this->taskId,
                $this->userId,
                20,
                "分析分镜内容，共 {$totalStoryboards} 个分镜"
            );

            foreach ($storyboards as $index => $storyboard) {
                try {
                    // 计算当前进度 (20% - 90% 之间)
                    $currentProgress = 20 + (($index + 1) / $totalStoryboards) * 70;

                    // 推送当前分镜处理进度
                    $webSocketEventService->pushAiGenerationProgress(
                        $this->taskId,
                        $this->userId,
                        $currentProgress,
                        "正在生成第 " . ($index + 1) . " 个分镜图片"
                    );

                    // 标记分镜为生成中
                    $storyboard->markAsGenerating();

                    // 获取分镜提示词
                    $prompt = $storyboard->ai_prompt ?: $storyboard->scene_description;
                    if (empty($prompt)) {
                        throw new \Exception("分镜 {$storyboard->id} 缺少提示词");
                    }

                    // 构建图片生成参数
                    $imageGenerationParams = [
                        'style' => $this->generationParams['style'] ?? null,
                        'aspect_ratio' => $this->generationParams['aspect_ratio'] ?? '16:9',
                        'quality' => $this->generationParams['quality'] ?? 'standard',
                        'platform' => $this->generationParams['platform'] ?? 'liblib'
                    ];

                    // 调用 ImageService 进行图片生成
                    $result = $imageService->generateImage(
                        $this->userId,
                        $prompt,
                        null, // character_id
                        $storyboard->project_id,
                        $imageGenerationParams
                    );

                    if ($result['code'] === ApiCodeEnum::SUCCESS) {
                        // 生成成功
                        $storyboard->markAsCompleted();

                        // 保存生成的资源ID（如果有）
                        if (isset($result['data']['resource_id']) && $result['data']['resource_id']) {
                            $storyboard->generated_image_id = $result['data']['resource_id'];
                        }

                        // 保存生成信息到元数据中
                        $metadata = $storyboard->metadata ?? [];
                        $metadata['image_generation_result'] = [
                            'image_url' => $result['data']['image_url'] ?? '',
                            'thumbnail_url' => $result['data']['thumbnail_url'] ?? '',
                            'cost' => $result['data']['cost'] ?? 0,
                            'platform' => $result['data']['platform'] ?? '',
                            'generated_at' => now()->toISOString(),
                            'task_id' => $this->taskId
                        ];
                        $storyboard->metadata = $metadata;
                        $storyboard->save();

                        $successCount++;
                        $totalCost += $result['data']['cost'] ?? 0;

                        Log::info('分镜图片生成成功', [
                            'task_id' => $this->taskId,
                            'storyboard_id' => $storyboard->id,
                            'user_id' => $this->userId,
                            'cost' => $result['data']['cost'] ?? 0
                        ]);

                    } else {
                        // 生成失败
                        $storyboard->markAsFailed();
                        $failedCount++;

                        Log::error('分镜图片生成失败', [
                            'task_id' => $this->taskId,
                            'storyboard_id' => $storyboard->id,
                            'user_id' => $this->userId,
                            'error' => $result['message'] ?? '未知错误'
                        ]);
                    }

                    $processedCount++;

                } catch (\Exception $e) {
                    $storyboard->markAsFailed();
                    $failedCount++;
                    $processedCount++;

                    Log::error('分镜图片生成异常', [
                        'task_id' => $this->taskId,
                        'storyboard_id' => $storyboard->id,
                        'user_id' => $this->userId,
                        'error' => $e->getMessage()
                    ]);
                }
            }

            // 推送完成进度
            $webSocketEventService->pushAiGenerationProgress(
                $this->taskId,
                $this->userId,
                100,
                "分镜图片生成完成"
            );

            // 推送任务完成事件
            $webSocketEventService->pushAiGenerationCompleted(
                $this->taskId,
                $this->userId
            );

            DB::commit();

            Log::info('分镜图片生成任务完成', [
                'task_id' => $this->taskId,
                'user_id' => $this->userId,
                'total_storyboards' => $totalStoryboards,
                'success_count' => $successCount,
                'failed_count' => $failedCount,
                'total_cost' => $totalCost
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            // 推送任务失败事件
            $webSocketEventService->pushAiGenerationFailed(
                $this->taskId,
                $this->userId,
                $e->getMessage()
            );

            // 发布失败事件到事件总线
            $this->publishFailureEvent($e->getMessage());

            Log::error('分镜图片生成任务失败', [
                'task_id' => $this->taskId,
                'user_id' => $this->userId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);

            throw $e;
        }
    }

    /**
     * 任务失败处理
     */
    public function failed(\Throwable $exception)
    {
        $webSocketEventService = app(WebSocketEventService::class);

        // 推送任务失败事件
        $webSocketEventService->pushAiGenerationFailed(
            $this->taskId,
            $this->userId,
            $exception->getMessage()
        );

        // 发布最终失败事件
        $this->publishFailureEvent($exception->getMessage());

        Log::error('分镜图片生成任务最终失败', [
            'task_id' => $this->taskId,
            'user_id' => $this->userId,
            'error' => $exception->getMessage()
        ]);
    }

    /**
     * 发布失败事件到事件总线
     */
    private function publishFailureEvent(string $errorMessage): void
    {
        try {
            // 调用事件发布API
            $response = Http::withHeaders([
                'Authorization' => 'Bearer ' . config('app.internal_api_token', 'internal_service_token'),
                'Content-Type' => 'application/json'
            ])->post(config('app.url') . '/py-api/events/publish', [
                'event_type' => 'storyboard_generation_failed',
                'business_id' => $this->taskId,
                'user_id' => $this->userId,
                'error_details' => [
                    'error_message' => $errorMessage,
                    'task_id' => $this->taskId,
                    'mode' => $this->mode,
                    'storyboard_count' => count($this->storyboardIds)
                ],
                'metadata' => [
                    'storyboard_ids' => $this->storyboardIds,
                    'generation_params' => $this->generationParams,
                    'mode' => $this->mode,
                    'failed_at' => now()->toISOString()
                ]
            ]);

            if ($response->successful()) {
                Log::info('失败事件发布成功', [
                    'task_id' => $this->taskId,
                    'event_response' => $response->json()
                ]);
            } else {
                Log::warning('失败事件发布失败', [
                    'task_id' => $this->taskId,
                    'response_status' => $response->status(),
                    'response_body' => $response->body()
                ]);
            }

        } catch (\Exception $e) {
            Log::error('发布失败事件异常', [
                'task_id' => $this->taskId,
                'error' => $e->getMessage()
            ]);
        }
    }
}
