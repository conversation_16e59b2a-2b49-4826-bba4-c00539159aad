<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 修复WebSocket会话表结构
 * 
 * 🔧 修复内容：
 * ❌ 删除冗余字段：connection_id, ip_address, last_activity_at, connection_params, metadata
 * ✅ 添加缺失字段：business_type, client_version, subscribed_events, message_count, disconnect_reason
 * 🔄 重命名字段：无需重命名（实际表已使用connection_ip）
 * 📝 调整字段类型和约束以匹配实际使用情况
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('websocket_sessions', function (Blueprint $table) {
            // 删除冗余字段（如果存在）
            if (Schema::hasColumn('websocket_sessions', 'connection_id')) {
                $table->dropIndex('idx_connection_id');
                $table->dropColumn('connection_id');
            }
            
            if (Schema::hasColumn('websocket_sessions', 'ip_address')) {
                $table->dropColumn('ip_address');
            }
            
            if (Schema::hasColumn('websocket_sessions', 'last_activity_at')) {
                $table->dropIndex('idx_last_activity');
                $table->dropIndex('idx_status_activity');
                $table->dropColumn('last_activity_at');
            }
            
            if (Schema::hasColumn('websocket_sessions', 'connection_params')) {
                $table->dropColumn('connection_params');
            }
            
            if (Schema::hasColumn('websocket_sessions', 'metadata')) {
                $table->dropColumn('metadata');
            }
            
            // 添加缺失字段
            if (!Schema::hasColumn('websocket_sessions', 'business_type')) {
                $table->string('business_type', 50)->nullable()->after('client_type')
                    ->comment('业务类型：text_generation, storyboard_generation, project_creation等');
            }
            
            if (!Schema::hasColumn('websocket_sessions', 'client_version')) {
                $table->string('client_version', 20)->nullable()->after('client_type')
                    ->comment('客户端版本');
            }
            
            if (!Schema::hasColumn('websocket_sessions', 'connection_ip')) {
                $table->string('connection_ip', 45)->nullable()->after('user_agent')
                    ->comment('客户端IP地址');
            }
            
            if (!Schema::hasColumn('websocket_sessions', 'connection_info')) {
                $table->json('connection_info')->nullable()->after('status')
                    ->comment('连接信息（JSON格式）');
            }
            
            if (!Schema::hasColumn('websocket_sessions', 'subscribed_events')) {
                $table->json('subscribed_events')->nullable()->after('connection_info')
                    ->comment('订阅的事件列表（JSON格式）');
            }
            
            if (!Schema::hasColumn('websocket_sessions', 'last_ping_at')) {
                $table->timestamp('last_ping_at')->nullable()->after('connected_at')
                    ->comment('最后心跳时间');
            }
            
            if (!Schema::hasColumn('websocket_sessions', 'message_count')) {
                $table->integer('message_count')->default(0)->after('disconnected_at')
                    ->comment('消息计数');
            }
            
            if (!Schema::hasColumn('websocket_sessions', 'disconnect_reason')) {
                $table->text('disconnect_reason')->nullable()->after('message_count')
                    ->comment('断开连接原因');
            }
        });
        
        // 重新创建索引
        Schema::table('websocket_sessions', function (Blueprint $table) {
            // 添加新的索引
            try {
                $table->index(['user_id', 'business_type'], 'idx_user_business_type');
            } catch (\Exception $e) {
                // 索引可能已存在，忽略错误
            }

            try {
                $table->index('last_ping_at', 'idx_last_ping');
            } catch (\Exception $e) {
                // 索引可能已存在，忽略错误
            }

            try {
                $table->index(['status', 'last_ping_at'], 'idx_status_ping');
            } catch (\Exception $e) {
                // 索引可能已存在，忽略错误
            }
        });
        
        // 调整status字段的枚举值以匹配实际使用
        Schema::table('websocket_sessions', function (Blueprint $table) {
            // 先删除原有的status字段
            $table->dropColumn('status');
        });
        
        Schema::table('websocket_sessions', function (Blueprint $table) {
            // 重新创建status字段，使用正确的枚举值
            $table->enum('status', ['connected', 'disconnected', 'timeout'])
                ->default('connected')->after('user_agent')
                ->comment('连接状态');
                
            // 重新创建status相关索引
            $table->index('status', 'idx_websocket_status');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('websocket_sessions', function (Blueprint $table) {
            // 恢复原有字段
            $table->string('connection_id', 100)->after('user_id')->comment('连接ID');
            $table->string('ip_address', 45)->nullable()->after('status')->comment('客户端IP地址');
            $table->timestamp('last_activity_at')->useCurrent()->after('connected_at')->comment('最后活动时间');
            $table->json('connection_params')->nullable()->after('disconnected_at')->comment('连接参数');
            $table->json('metadata')->nullable()->after('connection_params')->comment('会话元数据');
            
            // 删除新增字段
            $table->dropColumn([
                'business_type',
                'client_version', 
                'connection_ip',
                'connection_info',
                'subscribed_events',
                'last_ping_at',
                'message_count',
                'disconnect_reason'
            ]);
            
            // 恢复原有索引
            $table->index('connection_id', 'idx_connection_id');
            $table->index('last_activity_at', 'idx_last_activity');
            $table->index(['status', 'last_activity_at'], 'idx_status_activity');
        });
        
        // 恢复原有status枚举值
        Schema::table('websocket_sessions', function (Blueprint $table) {
            $table->dropColumn('status');
        });
        
        Schema::table('websocket_sessions', function (Blueprint $table) {
            $table->enum('status', ['connected', 'disconnected', 'error'])
                ->default('connected')->after('client_type')
                ->comment('连接状态');
        });
    }

};
