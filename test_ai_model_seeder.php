<?php

// 临时脚本：插入测试AI模型配置
require_once 'php/api/vendor/autoload.php';

// 启动Laravel应用
$app = require_once 'php/api/bootstrap/app.php';

use Illuminate\Support\Facades\DB;

try {
    // 检查AI模型配置是否存在
    $modelExists = DB::table('ai_model_configs')
        ->where('platform', 'test_platform')
        ->where('model_name', 'test-text-model')
        ->exists();

    if (!$modelExists) {
        // 插入测试AI模型配置
        DB::table('ai_model_configs')->insert([
            'platform' => 'test_platform',
            'model_name' => 'test-text-model',
            'model_type' => 'text',
            'api_endpoint' => 'https://api.test.com/v1/chat/completions',
            'config_params' => json_encode([
                'api_key' => 'test_key',
                'base_url' => 'https://api.test.com'
            ]),
            'capabilities' => json_encode(['text_generation', 'chat']),
            'is_active' => 1,
            'is_default' => 1,
            'priority' => 10,
            'cost_per_request' => 0.01,
            'max_tokens' => 4000,
            'timeout_seconds' => 30,
            'health_status' => 'healthy',
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ]);
        echo "AI模型配置创建成功\n";
    } else {
        echo "AI模型配置已存在\n";
    }

    // 验证插入结果
    $count = DB::table('ai_model_configs')->count();
    echo "当前AI模型配置总数: {$count}\n";

    // 给用户ID 13添加积分
    $userId = 13;
    $currentPoints = DB::table('users')->where('id', $userId)->value('points');

    if ($currentPoints === null) {
        echo "用户 {$userId} 不存在\n";
    } else {
        // 增加积分
        $newPoints = $currentPoints + 1000;
        DB::table('users')->where('id', $userId)->update([
            'points' => $newPoints,
            'updated_at' => date('Y-m-d H:i:s')
        ]);
        echo "为用户 {$userId} 增加积分: {$currentPoints} -> {$newPoints}\n";
    }

} catch (Exception $e) {
    echo "错误: " . $e->getMessage() . "\n";
}
