<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 创建AI生成任务表
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('ai_generation_tasks', function (Blueprint $table) {
            $table->id()->comment('主键ID');
            $table->string('task_id', 100)->unique()->comment('任务唯一标识');
            $table->enum('task_type', ['video', 'audio', 'image', 'character', 'style', 'storyboard'])->comment('任务类型');
            $table->enum('status', ['pending', 'processing', 'completed', 'failed', 'cancelled'])->default('pending')->comment('任务状态');
            $table->unsignedBigInteger('user_id')->comment('用户ID，关联p_users表');
            $table->string('platform', 50)->comment('AI平台');
            $table->string('model_name', 100)->comment('使用的模型名称');
            $table->json('input_params')->comment('输入参数');
            $table->json('output_data')->nullable()->comment('输出数据');
            $table->json('error_details')->nullable()->comment('错误详情');
            $table->json('metadata')->nullable()->comment('任务元数据');
            $table->integer('progress')->default(0)->comment('进度百分比');
            $table->decimal('cost_points', 10, 2)->default(0.00)->comment('消耗积分');
            $table->integer('estimated_duration')->nullable()->comment('预估时长（秒）');
            $table->integer('actual_duration')->nullable()->comment('实际时长（秒）');
            $table->timestamp('started_at')->nullable()->comment('开始时间');
            $table->timestamp('completed_at')->nullable()->comment('完成时间');
            $table->timestamps();

            // 索引
            $table->index(['user_id', 'status'], 'idx_user_status');
            $table->index(['task_type', 'status'], 'idx_type_status');
            $table->index(['platform', 'status'], 'idx_platform_status');
            $table->index(['status', 'created_at'], 'idx_status_created');
            $table->index(['started_at', 'status'], 'idx_started_status');
            $table->index(['completed_at', 'status'], 'idx_completed_status');
            $table->index('task_id', 'idx_task_id');

            // 外键
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');

            $table->comment('AI生成任务表 - 管理所有AI生成任务的状态和结果');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('ai_generation_tasks');
    }
};
