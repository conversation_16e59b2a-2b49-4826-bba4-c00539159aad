<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;

/**
 * 系统事件模型
 * 
 * @property int $id
 * @property string $event_id
 * @property string $event_type
 * @property string $business_id
 * @property int $user_id
 * @property array $error_details
 * @property array $metadata
 * @property string $source
 * @property string $version
 * @property string $status
 * @property \Carbon\Carbon $processed_at
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class SystemEvent extends Model
{
    /**
     * 表名
     */
    protected $table = 'system_events';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'event_id',
        'event_type',
        'business_id',
        'user_id',
        'error_details',
        'metadata',
        'source',
        'version',
        'status',
        'processed_at'
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'user_id' => 'integer',
        'error_details' => 'array',
        'metadata' => 'array',
        'processed_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 默认值
     */
    protected $attributes = [
        'source' => 'api_service',
        'version' => '1.0',
        'status' => 'pending'
    ];

    /**
     * 事件类型常量
     */
    const TYPE_AI_GENERATION_STARTED = 'ai_generation_started';
    const TYPE_AI_GENERATION_COMPLETED = 'ai_generation_completed';
    const TYPE_AI_GENERATION_FAILED = 'ai_generation_failed';
    const TYPE_STORYBOARD_GENERATION_STARTED = 'storyboard_generation_started';
    const TYPE_STORYBOARD_GENERATION_COMPLETED = 'storyboard_generation_completed';
    const TYPE_STORYBOARD_GENERATION_FAILED = 'storyboard_generation_failed';
    const TYPE_AI_SERVICE_ERROR = 'ai_service_error';
    const TYPE_POINTS_CHANGED = 'points_changed';
    const TYPE_USER_REGISTERED = 'user_registered';
    const TYPE_USER_LOGIN = 'user_login';

    /**
     * 状态常量
     */
    const STATUS_PENDING = 'pending';
    const STATUS_PROCESSED = 'processed';
    const STATUS_FAILED = 'failed';

    /**
     * 来源常量
     */
    const SOURCE_API_SERVICE = 'api_service';
    const SOURCE_WEBSOCKET_SERVICE = 'websocket_service';
    const SOURCE_BACKGROUND_JOB = 'background_job';
    const SOURCE_CRON_JOB = 'cron_job';

    /**
     * 关联用户
     */
    public function user()
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 获取错误详情
     */
    public function getErrorDetail(string $key, $default = null)
    {
        return data_get($this->error_details, $key, $default);
    }

    /**
     * 获取元数据
     */
    public function getMetadata(string $key, $default = null)
    {
        return data_get($this->metadata, $key, $default);
    }

    /**
     * 标记为已处理
     */
    public function markAsProcessed(): void
    {
        $this->update([
            'status' => self::STATUS_PROCESSED,
            'processed_at' => Carbon::now()
        ]);
    }

    /**
     * 标记为失败
     */
    public function markAsFailed(array $errorDetails = []): void
    {
        $this->update([
            'status' => self::STATUS_FAILED,
            'error_details' => array_merge($this->error_details ?? [], $errorDetails),
            'processed_at' => Carbon::now()
        ]);
    }

    /**
     * 是否已处理
     */
    public function isProcessed(): bool
    {
        return $this->status === self::STATUS_PROCESSED;
    }

    /**
     * 是否失败
     */
    public function isFailed(): bool
    {
        return $this->status === self::STATUS_FAILED;
    }

    /**
     * 是否待处理
     */
    public function isPending(): bool
    {
        return $this->status === self::STATUS_PENDING;
    }

    /**
     * 作用域：按事件类型筛选
     */
    public function scopeByEventType($query, string $eventType)
    {
        return $query->where('event_type', $eventType);
    }

    /**
     * 作用域：按业务ID筛选
     */
    public function scopeByBusinessId($query, string $businessId)
    {
        return $query->where('business_id', $businessId);
    }

    /**
     * 作用域：按用户筛选
     */
    public function scopeByUser($query, int $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeByStatus($query, string $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 作用域：待处理的事件
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * 作用域：已处理的事件
     */
    public function scopeProcessed($query)
    {
        return $query->where('status', self::STATUS_PROCESSED);
    }

    /**
     * 作用域：失败的事件
     */
    public function scopeFailed($query)
    {
        return $query->where('status', self::STATUS_FAILED);
    }

    /**
     * 作用域：最近的事件
     */
    public function scopeRecent($query, int $hours = 24)
    {
        return $query->where('created_at', '>=', Carbon::now()->subHours($hours));
    }

    /**
     * 作用域：按创建时间排序
     */
    public function scopeOrderByCreated($query, string $direction = 'desc')
    {
        return $query->orderBy('created_at', $direction);
    }

    /**
     * 创建新事件
     */
    public static function createEvent(
        string $eventType,
        string $businessId,
        int $userId,
        array $metadata = [],
        array $errorDetails = [],
        string $source = self::SOURCE_API_SERVICE
    ): self {
        $eventId = 'evt_' . time() . '_' . substr(md5($businessId . $eventType), 0, 8);

        return self::create([
            'event_id' => $eventId,
            'event_type' => $eventType,
            'business_id' => $businessId,
            'user_id' => $userId,
            'metadata' => $metadata,
            'error_details' => $errorDetails,
            'source' => $source
        ]);
    }

    /**
     * 获取事件统计
     */
    public static function getEventStats(int $days = 7): array
    {
        $startDate = Carbon::now()->subDays($days);

        return [
            'total' => self::where('created_at', '>=', $startDate)->count(),
            'pending' => self::pending()->where('created_at', '>=', $startDate)->count(),
            'processed' => self::processed()->where('created_at', '>=', $startDate)->count(),
            'failed' => self::failed()->where('created_at', '>=', $startDate)->count(),
            'by_type' => self::where('created_at', '>=', $startDate)
                ->selectRaw('event_type, COUNT(*) as count')
                ->groupBy('event_type')
                ->orderByDesc('count')
                ->get()
                ->pluck('count', 'event_type')
                ->toArray()
        ];
    }
}
