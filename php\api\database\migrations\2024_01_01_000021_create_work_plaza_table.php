<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 创建作品广场表
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('work_plaza', function (Blueprint $table) {
            $table->id()->comment('主键ID');
            $table->unsignedBigInteger('work_id')->unique()->comment('作品ID，关联p_user_works表');
            $table->enum('display_status', ['featured', 'normal', 'hidden'])->default('normal')->comment('展示状态');
            $table->integer('quality_score')->default(0)->comment('质量评分');
            $table->integer('popularity_score')->default(0)->comment('热度评分');
            $table->integer('trending_score')->default(0)->comment('趋势评分');
            $table->json('recommendation_tags')->nullable()->comment('推荐标签');
            $table->string('featured_reason', 200)->nullable()->comment('推荐理由');
            $table->timestamp('featured_at')->nullable()->comment('推荐时间');
            $table->timestamp('featured_expires_at')->nullable()->comment('推荐过期时间');
            $table->unsignedBigInteger('featured_by')->nullable()->comment('推荐人ID，关联p_users表');
            $table->integer('position_weight')->default(0)->comment('位置权重');
            $table->json('display_metadata')->nullable()->comment('展示元数据');
            $table->timestamps();

            // 索引
            $table->index(['display_status', 'quality_score'], 'idx_status_quality');
            $table->index(['display_status', 'popularity_score'], 'idx_status_popularity');
            $table->index(['display_status', 'trending_score'], 'idx_status_trending');
            $table->index(['featured_at', 'featured_expires_at'], 'idx_featured_period');
            $table->index(['position_weight', 'display_status'], 'idx_weight_status');
            $table->index('work_id', 'idx_work_id');

            // 外键
            $table->foreign('work_id')->references('id')->on('user_works')->onDelete('cascade');
            $table->foreign('featured_by')->references('id')->on('users')->onDelete('set null');

            $table->comment('作品广场表 - 管理作品在广场中的展示和推荐');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('work_plaza');
    }
};
