<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 创建用户作品表
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_works', function (Blueprint $table) {
            $table->id()->comment('主键ID');
            $table->string('title', 200)->comment('作品标题');
            $table->text('description')->nullable()->comment('作品描述');
            $table->enum('type', ['video', 'image', 'audio', 'storyboard', 'project'])->comment('作品类型');
            $table->string('category', 50)->comment('作品分类');
            $table->unsignedBigInteger('user_id')->comment('创作者ID，关联p_users表');
            $table->unsignedBigInteger('project_id')->nullable()->comment('关联项目ID，关联p_projects表');
            $table->string('cover_image', 255)->nullable()->comment('封面图片');
            $table->json('content_urls')->nullable()->comment('内容URL集合');
            $table->json('metadata')->nullable()->comment('作品元数据');
            $table->json('tags')->nullable()->comment('标签集合');
            $table->enum('status', ['draft', 'published', 'private', 'deleted'])->default('draft')->comment('作品状态');
            $table->boolean('is_featured')->default(false)->comment('是否推荐作品');
            $table->boolean('allow_comments')->default(true)->comment('是否允许评论');
            $table->boolean('allow_download')->default(false)->comment('是否允许下载');
            $table->integer('view_count')->default(0)->comment('查看次数');
            $table->integer('like_count')->default(0)->comment('点赞次数');
            $table->integer('comment_count')->default(0)->comment('评论次数');
            $table->integer('share_count')->default(0)->comment('分享次数');
            $table->integer('download_count')->default(0)->comment('下载次数');
            $table->decimal('rating', 3, 2)->default(0.00)->comment('评分');
            $table->integer('rating_count')->default(0)->comment('评分次数');
            $table->timestamp('published_at')->nullable()->comment('发布时间');
            $table->timestamps();

            // 索引
            $table->index(['user_id', 'status'], 'idx_user_status');
            $table->index(['type', 'status'], 'idx_type_status');
            $table->index(['category', 'status'], 'idx_category_status');
            $table->index(['project_id', 'status'], 'idx_project_status');
            $table->index(['is_featured', 'status'], 'idx_featured_status');
            $table->index(['view_count', 'status'], 'idx_view_status');
            $table->index(['like_count', 'status'], 'idx_like_status');
            $table->index(['rating', 'rating_count'], 'idx_rating');
            $table->index(['published_at', 'status'], 'idx_published_status');
            $table->index('created_at', 'idx_created');

            // 外键
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('project_id')->references('id')->on('projects')->onDelete('set null');

            $table->comment('用户作品表 - 管理用户创作的各种作品');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_works');
    }
};
