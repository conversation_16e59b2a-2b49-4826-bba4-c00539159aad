<?php

namespace App\Http\Controllers\PyApi;

use App\Enums\ApiCodeEnum;
use App\Http\Controllers\Controller;
use App\Services\AuthService;
use App\Services\PyApi\StoryService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;
use App\Helpers\LogCheckHelper;

/**
 * AI故事生成与状态查询
 */
class StoryController extends Controller
{
    protected $storyService;

    public function __construct(StoryService $storyService)
    {
        $this->storyService = $storyService;
    }

    /**
     * @ApiTitle(故事生成)
     * @ApiSummary(使用AI生成故事内容)
     * @ApiMethod(POST)
     * @ApiRoute(/py-api/stories/generate)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="prompt", type="string", required=true, description="故事生成提示词")
     * @ApiParams(name="style_id", type="int", required=false, description="风格ID")
     * @ApiParams(name="project_id", type="int", required=false, description="关联项目ID")
     * @ApiParams(name="length", type="string", required=false, description="故事长度：short/medium/long")
     * @ApiParams(name="genre", type="string", required=false, description="故事类型")
     * @ApiParams(name="platform", type="string", required=false, description="指定AI平台：deepseek/minimax（可选，不指定时使用智能推荐）")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="数据")
     * @ApiReturnParams (name="data.task_id", type="int", required=true, description="任务ID")
     * @ApiReturnParams (name="data.status", type="string", required=true, description="任务状态")
     * @ApiReturnParams (name="data.story_content", type="string", required=false, description="生成的故事内容")
     * @ApiReturnParams (name="data.scenes", type="array", required=false, description="分镜场景列表")
     * @ApiReturnParams (name="data.characters", type="array", required=false, description="角色列表")
     * @ApiReturnParams (name="data.cost", type="decimal", required=false, description="消耗的积分")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "故事生成成功",
     *   "data": {
     *     "task_id": 123,
     *     "status": "completed",
     *     "story_content": "这是AI生成的故事内容...",
     *     "scenes": [
     *       {
     *         "scene_number": 1,
     *         "description": "场景描述",
     *         "characters": ["角色1", "角色2"],
     *         "duration": "30秒"
     *       }
     *     ],
     *     "characters": [
     *       {
     *         "name": "主角",
     *         "description": "角色描述",
     *         "personality": "性格特点"
     *       }
     *     ],
     *     "cost": "0.0150"
     *   }
     * })
     */
    public function generate(Request $request)
    {
        try {
            // 🚨 升级：获取支持的平台列表进行动态验证
            $supportedPlatforms = \App\Services\AiServiceClient::getSupportedPlatforms();
            $storyGenerationPlatforms = [];
            foreach ($supportedPlatforms as $platform) {
                $platformConfig = config("ai.platforms.{$platform}");
                if ($platformConfig && in_array('text_generation', $platformConfig['supports'] ?? [])) {
                    $storyGenerationPlatforms[] = $platform;
                }
            }

            $rules = [
                'prompt' => 'required|string|min:5|max:2000',
                'style_id' => 'sometimes|integer|exists:style_library,id',
                'project_id' => 'sometimes|integer|exists:projects,id',
                'length' => 'sometimes|string|in:short,medium,long',
                'genre' => 'sometimes|string|max:50',
                'platform' => 'sometimes|string|in:' . implode(',', $storyGenerationPlatforms)
            ];

            $messages = [
                'prompt.required' => '故事提示词不能为空',
                'prompt.min' => '故事提示词至少5个字符',
                'prompt.max' => '故事提示词不能超过2000个字符',
                'style_id.exists' => '风格不存在',
                'project_id.exists' => '项目不存在',
                'length.in' => '故事长度必须是：short、medium、long之一',
                'platform.in' => 'AI平台必须是：' . implode('、', $storyGenerationPlatforms) . '之一'
            ];

            $this->validateData($request->all(), $rules, $messages, []);

            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];

            $generationParams = [
                'length' => $request->get('length', 'medium'),
                'genre' => $request->get('genre'),
                'platform' => $request->get('platform', 'deepseek')
            ];

            $result = $this->storyService->generateStory(
                $user->id,
                $request->prompt,
                $request->style_id,
                $request->project_id,
                $generationParams
            );

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('AI故事生成失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, 'AI故事生成失败', []);
        }
    }

    /**
     * @ApiTitle(故事生成状态查询)
     * @ApiSummary(查询故事生成任务的状态和结果)
     * @ApiMethod(GET)
     * @ApiRoute(/py-api/stories/{id}/status)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiParams(name="id", type="int", required=true, description="任务ID")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="任务数据")
     * @ApiReturn({
     *   "code": 200,
     *   "message": "success",
     *   "data": {
     *     "id": 123,
     *     "task_type": "text_generation_story",
     *     "status": "completed",
     *     "platform": "deepseek",
     *     "story_content": "生成的故事内容...",
     *     "scenes": [],
     *     "characters": [],
     *     "cost": "0.0150",
     *     "processing_time_ms": 1200,
     *     "created_at": "2024-01-01 12:00:00",
     *     "completed_at": "2024-01-01 12:00:01"
     *   }
     * })
     */
    public function getStatus(Request $request, $id)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $result = $this->storyService->getStoryStatus($id, $user->id);

            if ($result['code'] === ApiCodeEnum::SUCCESS) {
                return $this->successResponse($result['data'], $result['message']);
            } else {
                return $this->errorResponse($result['code'], $result['message'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取故事生成状态失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取故事生成状态失败', []);
        }
    }

    /**
     * @ApiTitle(获取故事生成平台选项)
     * @ApiSummary(获取可用的故事生成AI平台选项列表)
     * @ApiMethod(GET)
     * @ApiRoute(/py-api/ai-models/platform-options/text_generation)
     * @ApiHeaders(name="Authorization", type="string", required=true, description="API Token,格式：Bearer拼接空格再拼接token")
     * @ApiReturnParams (name="code", type="int", required=true, description="状态码")
     * @ApiReturnParams (name="message", type="string", required=true, description="消息")
     * @ApiReturnParams (name="data", type="object", required=true, description="平台选项数据")
     */
    public function getPlatformOptions(Request $request)
    {
        try {
            // 使用AuthService进行认证
            $authResult = AuthService::authenticate($request);
            if (!$authResult['success']) {
                return $this->errorResponse(ApiCodeEnum::UNAUTHORIZED, '认证失败');
            }

            $user = $authResult['user'];
            $taskType = 'text_generation';

            // 获取平台选项
            $result = \App\Services\AiServiceClient::getPlatformOptions($taskType, $user->id);

            if ($result['success']) {
                return $this->successResponse($result['data'], 'success');
            } else {
                return $this->errorResponse(ApiCodeEnum::MY_SERVICE_ERROR, $result['error'], $result['data']);
            }
        } catch (\Exception $e) {
            Log::error('获取故事生成平台选项失败', [
                'method' => __METHOD__,
                'user_id' => $user->id ?? null,
                'error_context' => LogCheckHelper::sanitize_request_for_log($request->all()),
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString()
            ]);
            return $this->errorResponse(ApiCodeEnum::CONTROLLER_ERROR, '获取平台选项失败');
        }
    }
}
