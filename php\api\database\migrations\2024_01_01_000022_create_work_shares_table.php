<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 创建作品分享表
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('work_shares', function (Blueprint $table) {
            $table->id()->comment('主键ID');
            $table->string('share_id', 100)->unique()->comment('分享唯一标识');
            $table->unsignedBigInteger('work_id')->comment('作品ID，关联p_user_works表');
            $table->unsignedBigInteger('user_id')->comment('分享者ID，关联p_users表');
            $table->enum('share_type', ['public', 'private', 'password', 'time_limited'])->comment('分享类型');
            $table->string('share_password', 50)->nullable()->comment('分享密码');
            $table->timestamp('expires_at')->nullable()->comment('过期时间');
            $table->boolean('allow_download')->default(false)->comment('是否允许下载');
            $table->boolean('allow_comments')->default(true)->comment('是否允许评论');
            $table->text('share_message')->nullable()->comment('分享留言');
            $table->json('permissions')->nullable()->comment('分享权限设置');
            $table->enum('status', ['active', 'expired', 'disabled'])->default('active')->comment('分享状态');
            $table->integer('view_count')->default(0)->comment('查看次数');
            $table->integer('download_count')->default(0)->comment('下载次数');
            $table->json('access_log')->nullable()->comment('访问日志');
            $table->timestamps();

            // 索引
            $table->index(['work_id', 'status'], 'idx_work_status');
            $table->index(['user_id', 'status'], 'idx_user_status');
            $table->index(['share_type', 'status'], 'idx_type_status');
            $table->index(['expires_at', 'status'], 'idx_expires_status');
            $table->index('share_id', 'idx_share_id');
            $table->index('created_at', 'idx_created');

            // 外键
            $table->foreign('work_id')->references('id')->on('user_works')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');

            $table->comment('作品分享表 - 管理作品的分享链接和权限');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('work_shares');
    }
};
