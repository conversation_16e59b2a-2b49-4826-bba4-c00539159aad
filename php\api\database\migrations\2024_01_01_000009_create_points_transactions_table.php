<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 创建积分交易表
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('points_transactions', function (Blueprint $table) {
            $table->id()->comment('主键ID');
            $table->string('transaction_id', 100)->unique()->comment('交易唯一标识');
            $table->unsignedBigInteger('user_id')->comment('用户ID，关联p_users表');
            $table->enum('type', ['earn', 'spend', 'freeze', 'unfreeze', 'refund', 'admin_adjust'])->comment('交易类型');
            $table->decimal('amount', 10, 2)->comment('交易金额');
            $table->decimal('balance_before', 10, 2)->comment('交易前余额');
            $table->decimal('balance_after', 10, 2)->comment('交易后余额');
            $table->string('source', 100)->comment('交易来源');
            $table->string('business_type', 50)->comment('业务类型');
            $table->string('business_id', 100)->nullable()->comment('业务ID');
            $table->text('description')->comment('交易描述');
            $table->json('metadata')->nullable()->comment('交易元数据');
            $table->enum('status', ['pending', 'completed', 'failed', 'cancelled'])->default('pending')->comment('交易状态');
            $table->timestamp('processed_at')->nullable()->comment('处理时间');
            $table->unsignedBigInteger('processed_by')->nullable()->comment('处理人ID，关联p_users表');
            $table->timestamps();

            // 索引
            $table->index(['user_id', 'type'], 'idx_user_type');
            $table->index(['user_id', 'created_at'], 'idx_user_created');
            $table->index(['type', 'status'], 'idx_type_status');
            $table->index(['business_type', 'business_id'], 'idx_business');
            $table->index(['status', 'created_at'], 'idx_status_created');
            $table->index(['source', 'created_at'], 'idx_source_created');
            $table->index('transaction_id', 'idx_transaction_id');

            // 外键
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('processed_by')->references('id')->on('users')->onDelete('set null');

            $table->comment('积分交易表 - 记录所有积分相关的交易');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('points_transactions');
    }
};
