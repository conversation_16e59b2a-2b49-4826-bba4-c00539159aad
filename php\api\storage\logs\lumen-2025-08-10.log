[2025-08-10 11:24:23] production.ERROR: Command "tinker" is not defined. {"exception":"[object] (Symfony\\Component\\Console\\Exception\\CommandNotFoundException(code: 0): Command \"tinker\" is not defined. at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php:737)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(266): Symfony\\Component\\Console\\Application->find('tinker')
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#4 {main}
"} 
[2025-08-10 11:29:59] production.ERROR: Class "Doctrine\DBAL\Driver\AbstractMySQLDriver" not found {"exception":"[object] (Error(code: 0): Class \"Doctrine\\DBAL\\Driver\\AbstractMySQLDriver\" not found at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\PDO\\MySqlDriver.php:8)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\composer\\ClassLoader.php(576): include()
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\composer\\ClassLoader.php(427): Composer\\Autoload\\{closure}('D:\\\\longtool\\\\php...')
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\MySqlConnection.php(118): Composer\\Autoload\\ClassLoader->loadClass('Illuminate\\\\Data...')
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(1265): Illuminate\\Database\\MySqlConnection->getDoctrineDriver()
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Connection.php(1252): Illuminate\\Database\\Connection->getDoctrineConnection()
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\database\\migrations\\2025_08_10_120000_fix_websocket_sessions_table_structure.php(171): Illuminate\\Database\\Connection->getDoctrineSchemaManager()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\database\\migrations\\2025_08_10_120000_fix_websocket_sessions_table_structure.php(93): Illuminate\\Database\\Migrations\\Migration@anonymous->indexExists('websocket_sessi...', 'idx_user_busine...')
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Schema\\Blueprint.php(96): Illuminate\\Database\\Migrations\\Migration@anonymous->{closure}(Object(Illuminate\\Database\\Schema\\Blueprint))
#8 [internal function]: Illuminate\\Database\\Schema\\Blueprint->__construct('websocket_sessi...', Object(Closure), '')
#9 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(952): ReflectionClass->newInstanceArgs(Array)
#10 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(795): Illuminate\\Container\\Container->build('Illuminate\\\\Data...')
#11 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(731): Illuminate\\Container\\Container->resolve('Illuminate\\\\Data...', Array)
#12 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Application.php(327): Illuminate\\Container\\Container->make('Illuminate\\\\Data...', Array)
#13 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Schema\\Builder.php(572): Laravel\\Lumen\\Application->make('Illuminate\\\\Data...', Array)
#14 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Schema\\Builder.php(394): Illuminate\\Database\\Schema\\Builder->createBlueprint('websocket_sessi...', Object(Closure))
#15 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\support\\Facades\\Facade.php(355): Illuminate\\Database\\Schema\\Builder->table('websocket_sessi...', Object(Closure))
#16 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\database\\migrations\\2025_08_10_120000_fix_websocket_sessions_table_structure.php(104): Illuminate\\Support\\Facades\\Facade::__callStatic('table', Array)
#17 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Migrations\\Migrator.php(493): Illuminate\\Database\\Migrations\\Migration@anonymous->up()
#18 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Migrations\\Migrator.php(410): Illuminate\\Database\\Migrations\\Migrator->runMethod(Object(Illuminate\\Database\\MySqlConnection), Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#19 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Migrations\\Migrator.php(419): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#20 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->runMigration(Object(Illuminate\\Database\\Migrations\\Migration@anonymous), 'up')
#21 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\View\\Components\\Task.php(37): Illuminate\\Database\\Migrations\\Migrator->Illuminate\\Database\\Migrations\\{closure}()
#22 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Migrations\\Migrator.php(756): Illuminate\\Console\\View\\Components\\Task->render('2025_08_10_1200...', Object(Closure))
#23 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Migrations\\Migrator.php(216): Illuminate\\Database\\Migrations\\Migrator->write('Illuminate\\\\Cons...', '2025_08_10_1200...', Object(Closure))
#24 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Migrations\\Migrator.php(181): Illuminate\\Database\\Migrations\\Migrator->runUp('D:\\\\longtool\\\\php...', 2, false)
#25 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Migrations\\Migrator.php(124): Illuminate\\Database\\Migrations\\Migrator->runPending(Array, Array)
#26 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Console\\Migrations\\MigrateCommand.php(92): Illuminate\\Database\\Migrations\\Migrator->run(Array, Array)
#27 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Migrations\\Migrator.php(633): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->Illuminate\\Database\\Console\\Migrations\\{closure}()
#28 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Console\\Migrations\\MigrateCommand.php(104): Illuminate\\Database\\Migrations\\Migrator->usingConnection(NULL, Object(Closure))
#29 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(36): Illuminate\\Database\\Console\\Migrations\\MigrateCommand->handle()
#30 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Util.php(41): Illuminate\\Container\\BoundMethod::Illuminate\\Container\\{closure}()
#31 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(93): Illuminate\\Container\\Util::unwrapIfClosure(Object(Closure))
#32 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\BoundMethod.php(37): Illuminate\\Container\\BoundMethod::callBoundMethod(Object(Laravel\\Lumen\\Application), Array, Object(Closure))
#33 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\container\\Container.php(662): Illuminate\\Container\\BoundMethod::call(Object(Laravel\\Lumen\\Application), Array, Array, NULL)
#34 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(211): Illuminate\\Container\\Container->call(Array)
#35 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Command\\Command.php(326): Illuminate\\Console\\Command->execute(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#36 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\console\\Command.php(181): Symfony\\Component\\Console\\Command\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Illuminate\\Console\\OutputStyle))
#37 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(1078): Illuminate\\Console\\Command->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#38 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(324): Symfony\\Component\\Console\\Application->doRunCommand(Object(Illuminate\\Database\\Console\\Migrations\\MigrateCommand), Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#39 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\symfony\\console\\Application.php(175): Symfony\\Component\\Console\\Application->doRun(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#40 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\laravel\\lumen-framework\\src\\Console\\Kernel.php(160): Symfony\\Component\\Console\\Application->run(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#41 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\artisan(35): Laravel\\Lumen\\Console\\Kernel->handle(Object(Symfony\\Component\\Console\\Input\\ArgvInput), Object(Symfony\\Component\\Console\\Output\\ConsoleOutput))
#42 {main}
"} 
[2025-08-10 11:33:00] production.ERROR: Call to a member function connection() on null {"exception":"[object] (Error(code: 0): Call to a member function connection() on null at D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php:1819)
[stacktrace]
#0 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(1785): Illuminate\\Database\\Eloquent\\Model::resolveConnection(NULL)
#1 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(1576): Illuminate\\Database\\Eloquent\\Model->getConnection()
#2 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(1495): Illuminate\\Database\\Eloquent\\Model->newBaseQueryBuilder()
#3 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(1531): Illuminate\\Database\\Eloquent\\Model->newModelQuery()
#4 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(1484): Illuminate\\Database\\Eloquent\\Model->newQueryWithoutScopes()
#5 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(2334): Illuminate\\Database\\Eloquent\\Model->newQuery()
#6 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\php\\api\\vendor\\illuminate\\database\\Eloquent\\Model.php(2346): Illuminate\\Database\\Eloquent\\Model->__call('create', Array)
#7 D:\\longtool\\phpStudy_64\\WWW\\tool_api\\test_websocket_model.php(31): Illuminate\\Database\\Eloquent\\Model::__callStatic('create', Array)
#8 {main}
"} 
