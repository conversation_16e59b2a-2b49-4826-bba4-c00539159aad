<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 综合修复多个表结构
 * 
 * 🔧 修复内容：
 * ✅ character_library表：删除冗余字段，添加缺失字段
 * ✅ user_character_bindings表：删除冗余字段，添加缺失字段
 * ✅ ai_model_configs表：删除冗余字段，调整字段类型
 * 📝 确保所有表结构与代码使用情况完全匹配
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // ==================== 修复 character_library 表 ====================
        Schema::table('character_library', function (Blueprint $table) {
            // 删除冗余字段
            if (Schema::hasColumn('character_library', 'avatar_url')) {
                $table->dropColumn('avatar_url');
            }
            if (Schema::hasColumn('character_library', 'category')) {
                $table->dropColumn('category');
            }
            if (Schema::hasColumn('character_library', 'style')) {
                $table->dropColumn('style');
            }
            if (Schema::hasColumn('character_library', 'background_story')) {
                $table->dropColumn('background_story');
            }
            if (Schema::hasColumn('character_library', 'generation_params')) {
                $table->dropColumn('generation_params');
            }
            if (Schema::hasColumn('character_library', 'project_id')) {
                $table->dropColumn('project_id');
            }
            if (Schema::hasColumn('character_library', 'is_public')) {
                $table->dropColumn('is_public');
            }
            if (Schema::hasColumn('character_library', 'source_file_id')) {
                $table->dropColumn('source_file_id');
            }
            if (Schema::hasColumn('character_library', 'usage_count')) {
                $table->dropColumn('usage_count');
            }
            if (Schema::hasColumn('character_library', 'metadata')) {
                $table->dropColumn('metadata');
            }
            
            // 添加缺失字段
            if (!Schema::hasColumn('character_library', 'category_id')) {
                $table->bigInteger('category_id')->unsigned()->after('description')
                    ->comment('角色分类ID，关联p_character_categories表');
            }
            if (!Schema::hasColumn('character_library', 'avatar')) {
                $table->string('avatar', 255)->nullable()->after('appearance')
                    ->comment('角色头像');
            }
            if (!Schema::hasColumn('character_library', 'images')) {
                $table->json('images')->nullable()->after('avatar')
                    ->comment('角色图片集合');
            }
            if (!Schema::hasColumn('character_library', 'voice_config')) {
                $table->json('voice_config')->nullable()->after('images')
                    ->comment('语音配置');
            }
            if (!Schema::hasColumn('character_library', 'style_preferences')) {
                $table->json('style_preferences')->nullable()->after('voice_config')
                    ->comment('风格偏好');
            }
            if (!Schema::hasColumn('character_library', 'is_premium')) {
                $table->boolean('is_premium')->default(false)->after('is_active')
                    ->comment('是否高级角色');
            }
            if (!Schema::hasColumn('character_library', 'sort_order')) {
                $table->integer('sort_order')->default(0)->after('is_featured')
                    ->comment('排序顺序');
            }
            if (!Schema::hasColumn('character_library', 'binding_count')) {
                $table->integer('binding_count')->default(0)->after('sort_order')
                    ->comment('绑定次数');
            }
            if (!Schema::hasColumn('character_library', 'rating_count')) {
                $table->integer('rating_count')->default(0)->after('rating')
                    ->comment('评分次数');
            }
        });
        
        // 调整字段类型
        Schema::table('character_library', function (Blueprint $table) {
            // 调整description字段为NOT NULL
            $table->text('description')->nullable(false)->change();
            
            // 调整background字段名称和类型
            if (Schema::hasColumn('character_library', 'background')) {
                $table->renameColumn('background', 'background');
            }
        });

        // ==================== 修复 user_character_bindings 表 ====================
        Schema::table('user_character_bindings', function (Blueprint $table) {
            // 删除冗余字段
            if (Schema::hasColumn('user_character_bindings', 'binding_type')) {
                $table->dropColumn('binding_type');
            }
            if (Schema::hasColumn('user_character_bindings', 'custom_settings')) {
                $table->dropColumn('custom_settings');
            }
            if (Schema::hasColumn('user_character_bindings', 'notes')) {
                $table->dropColumn('notes');
            }
            if (Schema::hasColumn('user_character_bindings', 'storyboard_position_id')) {
                $table->dropColumn('storyboard_position_id');
            }
            if (Schema::hasColumn('user_character_bindings', 'binding_context')) {
                $table->dropColumn('binding_context');
            }
            if (Schema::hasColumn('user_character_bindings', 'metadata')) {
                $table->dropColumn('metadata');
            }
            
            // 添加缺失字段
            if (!Schema::hasColumn('user_character_bindings', 'binding_name')) {
                $table->string('binding_name', 100)->nullable()->after('character_id')
                    ->comment('绑定名称');
            }
            if (!Schema::hasColumn('user_character_bindings', 'binding_reason')) {
                $table->string('binding_reason', 200)->nullable()->after('binding_name')
                    ->comment('绑定原因');
            }
            if (!Schema::hasColumn('user_character_bindings', 'custom_description')) {
                $table->text('custom_description')->nullable()->after('binding_reason')
                    ->comment('自定义描述');
            }
            if (!Schema::hasColumn('user_character_bindings', 'custom_config')) {
                $table->json('custom_config')->nullable()->after('custom_description')
                    ->comment('自定义配置');
            }
            if (!Schema::hasColumn('user_character_bindings', 'is_favorite')) {
                $table->boolean('is_favorite')->default(false)->after('is_active')
                    ->comment('是否收藏');
            }
            if (!Schema::hasColumn('user_character_bindings', 'user_rating')) {
                $table->decimal('user_rating', 3, 2)->nullable()->after('last_used_at')
                    ->comment('用户评分');
            }
            if (!Schema::hasColumn('user_character_bindings', 'user_feedback')) {
                $table->text('user_feedback')->nullable()->after('user_rating')
                    ->comment('用户反馈');
            }
        });

        // ==================== 修复 ai_model_configs 表 ====================
        Schema::table('ai_model_configs', function (Blueprint $table) {
            // 删除冗余字段（软删除字段）
            if (Schema::hasColumn('ai_model_configs', 'deleted_at')) {
                $table->dropColumn('deleted_at');
            }
            
            // 调整字段类型和默认值
            if (Schema::hasColumn('ai_model_configs', 'max_tokens')) {
                $table->integer('max_tokens')->nullable()->change();
            }
        });
        
        // 重新创建索引
        $this->recreateIndexes();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 恢复 character_library 表
        Schema::table('character_library', function (Blueprint $table) {
            // 恢复删除的字段
            $table->string('avatar_url', 500)->nullable()->comment('角色头像URL');
            $table->string('category', 50)->nullable()->comment('角色分类');
            $table->string('style', 100)->nullable()->comment('角色风格');
            $table->text('background_story')->nullable()->comment('背景故事');
            $table->json('generation_params')->nullable()->comment('生成参数');
            $table->bigInteger('project_id')->unsigned()->nullable()->comment('关联项目ID');
            $table->boolean('is_public')->default(false)->comment('是否公开');
            $table->string('source_file_id', 100)->nullable()->comment('源文件ID');
            $table->integer('usage_count')->default(0)->comment('使用次数');
            $table->json('metadata')->nullable()->comment('元数据');
            
            // 删除新增字段
            $table->dropColumn([
                'category_id', 'avatar', 'images', 'voice_config', 
                'style_preferences', 'is_premium', 'sort_order', 
                'binding_count', 'rating_count'
            ]);
        });
        
        // 恢复 user_character_bindings 表
        Schema::table('user_character_bindings', function (Blueprint $table) {
            // 恢复删除的字段
            $table->enum('binding_type', ['favorite', 'owned', 'created', 'customized'])->default('favorite');
            $table->json('custom_settings')->nullable();
            $table->text('notes')->nullable();
            $table->string('storyboard_position_id', 100)->nullable();
            $table->enum('binding_context', ['storyboard', 'project', 'library'])->default('library');
            $table->json('metadata')->nullable();
            
            // 删除新增字段
            $table->dropColumn([
                'binding_name', 'binding_reason', 'custom_description', 
                'custom_config', 'is_favorite', 'user_rating', 'user_feedback'
            ]);
        });
        
        // 恢复 ai_model_configs 表
        Schema::table('ai_model_configs', function (Blueprint $table) {
            $table->softDeletes();
        });
    }
    
    /**
     * 重新创建索引
     */
    private function recreateIndexes(): void
    {
        // character_library 表索引
        Schema::table('character_library', function (Blueprint $table) {
            try {
                $table->index('category_id', 'idx_character_category_id');
                $table->index(['is_premium', 'is_active'], 'idx_character_premium_active');
                $table->index('sort_order', 'idx_character_sort_order');
                $table->index('binding_count', 'idx_character_binding_count');
                $table->index('rating_count', 'idx_character_rating_count');
            } catch (\Exception $e) {
                // 索引可能已存在，忽略错误
            }
        });
        
        // user_character_bindings 表索引
        Schema::table('user_character_bindings', function (Blueprint $table) {
            try {
                $table->index('is_favorite', 'idx_binding_favorite');
                $table->index('user_rating', 'idx_binding_user_rating');
            } catch (\Exception $e) {
                // 索引可能已存在，忽略错误
            }
        });
    }
};
