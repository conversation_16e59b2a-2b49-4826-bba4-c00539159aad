<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 修复剩余表结构
 * 
 * 🔧 修复内容：
 * ✅ users表：删除冗余字段，添加缺失字段，调整字段长度和类型
 * ✅ system_events表：创建缺失的表（代码中有使用但表不存在）
 * 📝 确保所有表结构与代码使用情况完全匹配
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        // ==================== 修复 users 表 ====================
        Schema::table('users', function (Blueprint $table) {
            // 删除冗余字段（迁移文件中有但实际表中没有且代码中不使用的）
            if (Schema::hasColumn('users', 'phone')) {
                $table->dropColumn('phone');
            }
            if (Schema::hasColumn('users', 'email_verified_at')) {
                $table->dropColumn('email_verified_at');
            }
            if (Schema::hasColumn('users', 'phone_verified_at')) {
                $table->dropColumn('phone_verified_at');
            }
            if (Schema::hasColumn('users', 'register_ip')) {
                $table->dropColumn('register_ip');
            }
            if (Schema::hasColumn('users', 'register_source')) {
                $table->dropColumn('register_source');
            }
            if (Schema::hasColumn('users', 'preferences')) {
                $table->dropColumn('preferences');
            }
            if (Schema::hasColumn('users', 'metadata')) {
                $table->dropColumn('metadata');
            }
            if (Schema::hasColumn('users', 'remember_token')) {
                $table->dropColumn('remember_token');
            }
            
            // 添加缺失字段（实际表中有且代码中使用的）
            if (!Schema::hasColumn('users', 'frozen_points')) {
                $table->decimal('frozen_points', 10, 2)->default(0.00)->after('points')
                    ->comment('冻结积分');
            }
            if (!Schema::hasColumn('users', 'is_vip')) {
                $table->boolean('is_vip')->default(false)->after('frozen_points')
                    ->comment('是否VIP用户');
            }
            if (!Schema::hasColumn('users', 'vip_expires_at')) {
                $table->timestamp('vip_expires_at')->nullable()->after('is_vip')
                    ->comment('VIP过期时间');
            }
        });
        
        // 调整字段长度和类型
        Schema::table('users', function (Blueprint $table) {
            // 调整字段长度以匹配实际表结构
            $table->string('username', 50)->change();
            $table->string('email', 100)->nullable()->change();
            $table->string('nickname', 50)->nullable()->change();
            $table->string('avatar', 255)->nullable()->change();
            $table->string('remark', 255)->nullable()->change();
            
            // 调整status字段类型（从enum改为tinyint）
            $table->dropColumn('status');
        });
        
        Schema::table('users', function (Blueprint $table) {
            // 重新创建status字段为tinyint类型
            $table->tinyInteger('status')->default(1)->after('remark')
                ->comment('账户状态：0-禁用，1-启用');
                
            // 重新创建status索引
            $table->index('status', 'idx_status');
        });

        // ==================== 创建 system_events 表 ====================
        if (!Schema::hasTable('system_events')) {
            Schema::create('system_events', function (Blueprint $table) {
                $table->id()->comment('主键ID');
                $table->string('event_id', 100)->unique()->comment('事件唯一标识');
                $table->string('event_type', 100)->comment('事件类型');
                $table->string('business_id', 100)->comment('业务ID');
                $table->unsignedBigInteger('user_id')->comment('用户ID');
                $table->json('error_details')->nullable()->comment('错误详情（JSON格式）');
                $table->json('metadata')->nullable()->comment('元数据（JSON格式）');
                $table->string('source', 50)->default('api_service')->comment('事件来源');
                $table->string('version', 10)->default('1.0')->comment('事件版本');
                $table->enum('status', ['pending', 'processed', 'failed'])->default('pending')->comment('处理状态');
                $table->timestamp('processed_at')->nullable()->comment('处理时间');
                $table->timestamps();

                // 索引
                $table->index(['event_type', 'created_at'], 'idx_event_type_created');
                $table->index(['business_id', 'event_type'], 'idx_business_event');
                $table->index(['user_id', 'created_at'], 'idx_user_created');
                $table->index(['status', 'created_at'], 'idx_status_created');
                $table->index('event_id', 'idx_event_id');

                $table->comment('系统事件表 - 记录系统内部事件，支持事件驱动架构');
            });
        }
        
        // 重新创建users表的索引
        $this->recreateUsersIndexes();
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // 恢复 users 表
        Schema::table('users', function (Blueprint $table) {
            // 恢复删除的字段
            $table->string('phone', 20)->nullable()->comment('手机号码');
            $table->timestamp('email_verified_at')->nullable()->comment('邮箱验证时间');
            $table->timestamp('phone_verified_at')->nullable()->comment('手机验证时间');
            $table->string('register_ip', 45)->nullable()->comment('注册IP');
            $table->enum('register_source', ['web', 'py_tool', 'api', 'invitation'])->default('web')->comment('注册来源');
            $table->json('preferences')->nullable()->comment('用户偏好设置');
            $table->json('metadata')->nullable()->comment('用户元数据');
            $table->rememberToken();
            
            // 删除新增字段
            $table->dropColumn(['frozen_points', 'is_vip', 'vip_expires_at']);
            
            // 恢复字段长度
            $table->string('username', 100)->change();
            $table->string('email', 255)->change();
            $table->string('nickname', 100)->change();
            $table->string('avatar', 500)->change();
            $table->string('remark', 500)->change();
        });
        
        // 恢复status字段为enum类型
        Schema::table('users', function (Blueprint $table) {
            $table->dropColumn('status');
        });
        
        Schema::table('users', function (Blueprint $table) {
            $table->enum('status', ['active', 'inactive', 'banned'])->default('active')->comment('账户状态');
        });
        
        // 删除 system_events 表
        Schema::dropIfExists('system_events');
    }
    
    /**
     * 重新创建users表索引
     */
    private function recreateUsersIndexes(): void
    {
        Schema::table('users', function (Blueprint $table) {
            try {
                // 为新增字段创建索引
                $table->index('frozen_points', 'idx_frozen_points');
                $table->index('is_vip', 'idx_is_vip');
                $table->index('vip_expires_at', 'idx_vip_expires');
                $table->index(['is_vip', 'vip_expires_at'], 'idx_vip_status');
            } catch (\Exception $e) {
                // 索引可能已存在，忽略错误
            }
        });
    }
};
