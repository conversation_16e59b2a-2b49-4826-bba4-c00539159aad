<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * 积分交易模型
 * 
 * @property int $id
 * @property int $user_id
 * @property string $business_type
 * @property string $business_id
 * @property float $amount
 * @property string $status
 * @property string $ai_platform
 * @property array $request_data
 * @property array $response_data
 * @property int $timeout_seconds
 * @property \Carbon\Carbon $completed_at
 * @property string $failure_reason
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class PointsTransaction extends Model
{
    /**
     * 表名（框架会自动添加p_前缀）
     */
    protected $table = 'points_transactions';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'user_id',
        'business_type',
        'business_id',
        'amount',
        'status',
        'ai_platform',
        'request_data',
        'response_data',
        'timeout_seconds',
        'completed_at',
        'failure_reason'
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'amount' => 'decimal:2',
        'request_data' => 'array',
        'response_data' => 'array',
        'timeout_seconds' => 'integer',
        'completed_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 状态常量
     */
    const STATUS_FROZEN = 'frozen';
    const STATUS_SUCCESS = 'success';
    const STATUS_FAILED = 'failed';
    const STATUS_REFUNDED = 'refunded';

    /**
     * 业务类型常量
     */
    const TYPE_TEXT_TO_IMAGE = 'text_to_image';
    const TYPE_IMAGE_TO_VIDEO = 'image_to_video';
    const TYPE_VOICE_SYNTHESIS = 'voice_synthesis';
    const TYPE_TEXT_GENERATION = 'text_generation';

    /**
     * AI平台常量
     */
    const PLATFORM_DEEPSEEK = 'deepseek';
    const PLATFORM_LIBLIB = 'liblib';
    const PLATFORM_KLING = 'kling';
    const PLATFORM_MINIMAX = 'minimax';

    /**
     * 关联用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 检查是否已超时
     */
    public function isTimeout(): bool
    {
        if ($this->status !== self::STATUS_FROZEN) {
            return false;
        }

        return $this->created_at->addSeconds($this->timeout_seconds)->isPast();
    }

    /**
     * 获取剩余超时时间（秒）
     */
    public function getRemainingTimeoutSeconds(): int
    {
        if ($this->status !== self::STATUS_FROZEN) {
            return 0;
        }

        $timeoutAt = $this->created_at->addSeconds($this->timeout_seconds);
        return max(0, $timeoutAt->diffInSeconds(Carbon::now()));
    }

    /**
     * 获取状态描述
     */
    public function getStatusDescription(): string
    {
        $descriptions = [
            self::STATUS_FROZEN => '处理中',
            self::STATUS_SUCCESS => '成功',
            self::STATUS_FAILED => '失败',
            self::STATUS_REFUNDED => '已退款'
        ];

        return $descriptions[$this->status] ?? '未知状态';
    }

    /**
     * 获取业务类型描述
     */
    public function getBusinessTypeDescription(): string
    {
        $descriptions = [
            self::TYPE_TEXT_TO_IMAGE => '文本生成图片',
            self::TYPE_IMAGE_TO_VIDEO => '图片生成视频',
            self::TYPE_TEXT_GENERATION => '文本生成',
            self::TYPE_VOICE_SYNTHESIS => '语音合成'
        ];

        return $descriptions[$this->business_type] ?? '未知类型';
    }

    /**
     * 获取AI平台描述
     */
    public function getAiPlatformDescription(): string
    {
        $descriptions = [
            self::PLATFORM_DEEPSEEK => 'DeepSeek',
            self::PLATFORM_LIBLIB => 'LiblibAI',
            self::PLATFORM_KLING => 'KlingAI',
            self::PLATFORM_MINIMAX => 'MiniMaxi'
        ];

        return $descriptions[$this->ai_platform] ?? '未知平台';
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 作用域：按业务类型筛选
     */
    public function scopeByBusinessType($query, $type)
    {
        return $query->where('business_type', $type);
    }

    /**
     * 作用域：按AI平台筛选
     */
    public function scopeByAiPlatform($query, $platform)
    {
        return $query->where('ai_platform', $platform);
    }

    /**
     * 作用域：超时的交易
     */
    public function scopeTimeout($query)
    {
        return $query->where('status', self::STATUS_FROZEN)
            ->whereRaw('DATE_ADD(created_at, INTERVAL timeout_seconds SECOND) < NOW()');
    }

    /**
     * 作用域：今日交易
     */
    public function scopeToday($query)
    {
        return $query->whereDate('created_at', Carbon::today());
    }

    /**
     * 作用域：本月交易
     */
    public function scopeThisMonth($query)
    {
        return $query->whereMonth('created_at', Carbon::now()->month)
            ->whereYear('created_at', Carbon::now()->year);
    }
}
