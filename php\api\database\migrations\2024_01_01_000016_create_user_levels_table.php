<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 创建用户等级表
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_levels', function (Blueprint $table) {
            $table->id()->comment('主键ID');
            $table->integer('level')->unique()->comment('等级');
            $table->string('name', 100)->comment('等级名称');
            $table->text('description')->nullable()->comment('等级描述');
            $table->integer('experience_required')->comment('所需经验值');
            $table->string('icon', 255)->nullable()->comment('等级图标');
            $table->string('color', 20)->nullable()->comment('等级颜色');
            $table->json('privileges')->nullable()->comment('等级特权');
            $table->json('rewards')->nullable()->comment('升级奖励');
            $table->boolean('is_active')->default(true)->comment('是否激活');
            $table->timestamps();

            // 索引
            $table->index('level', 'idx_level');
            $table->index('experience_required', 'idx_experience');
            $table->index('is_active', 'idx_active');

            $table->comment('用户等级表 - 定义用户等级体系');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_levels');
    }
};
