<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 创建用户偏好设置表
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_preferences', function (Blueprint $table) {
            $table->id()->comment('主键ID');
            $table->unsignedBigInteger('user_id')->comment('用户ID，关联p_users表');
            $table->string('preference_key', 100)->comment('偏好键名');
            $table->json('preference_value')->comment('偏好值（JSON格式）');
            $table->string('category', 50)->comment('偏好分类（ui/notification/privacy/ai）');
            $table->boolean('is_default')->default(false)->comment('是否默认值');
            $table->timestamps();

            // 索引
            $table->index(['user_id', 'category'], 'idx_user_category');
            $table->index(['user_id', 'preference_key'], 'idx_user_key');
            $table->index('category', 'idx_category');

            // 外键
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');

            // 唯一约束
            $table->unique(['user_id', 'preference_key'], 'uk_user_preference');

            $table->comment('用户偏好设置表 - 存储用户的个性化配置');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_preferences');
    }
};
