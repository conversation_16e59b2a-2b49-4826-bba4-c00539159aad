<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 创建资源表
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('resources', function (Blueprint $table) {
            $table->id()->comment('主键ID');
            $table->string('name', 200)->comment('资源名称');
            $table->text('description')->nullable()->comment('资源描述');
            $table->enum('type', ['video', 'audio', 'image', 'character', 'style', 'template'])->comment('资源类型');
            $table->string('category', 50)->comment('资源分类');
            $table->string('file_path', 500)->nullable()->comment('文件路径');
            $table->string('file_url', 500)->nullable()->comment('文件URL');
            $table->string('thumbnail', 255)->nullable()->comment('缩略图');
            $table->bigInteger('file_size')->default(0)->comment('文件大小（字节）');
            $table->string('mime_type', 100)->nullable()->comment('MIME类型');
            $table->json('metadata')->nullable()->comment('资源元数据');
            $table->json('tags')->nullable()->comment('标签集合');
            $table->enum('status', ['pending', 'processing', 'completed', 'failed'])->default('pending')->comment('处理状态');
            $table->unsignedBigInteger('owner_id')->comment('所有者ID，关联p_users表');
            $table->boolean('is_public')->default(false)->comment('是否公开');
            $table->boolean('is_premium')->default(false)->comment('是否付费资源');
            $table->integer('download_count')->default(0)->comment('下载次数');
            $table->integer('view_count')->default(0)->comment('查看次数');
            $table->decimal('rating', 3, 2)->default(0.00)->comment('评分');
            $table->integer('rating_count')->default(0)->comment('评分次数');
            $table->timestamps();

            // 索引
            $table->index(['type', 'status'], 'idx_type_status');
            $table->index(['category', 'status'], 'idx_category_status');
            $table->index(['owner_id', 'status'], 'idx_owner_status');
            $table->index(['is_public', 'status'], 'idx_public_status');
            $table->index(['is_premium', 'is_public'], 'idx_premium_public');
            $table->index(['download_count', 'is_public'], 'idx_download_public');
            $table->index(['rating', 'rating_count'], 'idx_rating');
            $table->index('created_at', 'idx_created');

            // 外键
            $table->foreign('owner_id')->references('id')->on('users')->onDelete('cascade');

            $table->comment('资源表 - 管理各种类型的资源文件');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('resources');
    }
};
