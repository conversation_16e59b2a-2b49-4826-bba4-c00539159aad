<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 创建成就表
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('achievements', function (Blueprint $table) {
            $table->id()->comment('主键ID');
            $table->string('name', 100)->comment('成就名称');
            $table->text('description')->comment('成就描述');
            $table->string('icon', 255)->nullable()->comment('成就图标');
            $table->string('category', 50)->comment('成就分类');
            $table->enum('type', ['daily', 'weekly', 'monthly', 'milestone'])->comment('成就类型');
            $table->json('conditions')->comment('达成条件');
            $table->json('rewards')->comment('奖励内容');
            $table->integer('points')->default(0)->comment('奖励积分');
            $table->boolean('is_active')->default(true)->comment('是否激活');
            $table->integer('sort_order')->default(0)->comment('排序顺序');
            $table->timestamps();

            // 索引
            $table->index(['category', 'is_active'], 'idx_category_active');
            $table->index(['type', 'is_active'], 'idx_type_active');
            $table->index('sort_order', 'idx_sort_order');

            $table->comment('成就表 - 定义系统成就和奖励机制');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('achievements');
    }
};
