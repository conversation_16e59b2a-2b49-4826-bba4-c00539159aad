<?php

/**
 * 测试项目创建功能
 * 验证从分镜剧本创建项目的功能是否正常工作
 */

// 模拟AI返回的分镜剧本JSON数据
$mockStoryboardData = [
    "故事标题" => "小红帽的奇幻冒险",
    "故事简概" => "小红帽在去奶奶家的路上遇到了神奇的森林精灵，展开了一段奇幻的冒险之旅。",
    "场景1" => [
        "场景名称" => "小红帽的家",
        "空间" => "室内",
        "时间" => "早晨",
        "天气" => "晴朗",
        "场景提示词" => "温馨的小屋，阳光透过窗户洒进来",
        "分镜" => [
            [
                "分镜序号" => 1,
                "出境角色" => "小红帽",
                "字幕" => "小红帽准备出门去奶奶家",
                "分镜提示词" => "小红帽穿着红色斗篷，在家中准备篮子"
            ],
            [
                "分镜序号" => 2,
                "出境角色" => "小红帽, 妈妈",
                "字幕" => "妈妈叮嘱小红帽要小心",
                "分镜提示词" => "妈妈温柔地看着小红帽，给她篮子"
            ]
        ]
    ],
    "场景2" => [
        "场景名称" => "神秘森林",
        "空间" => "室外",
        "时间" => "上午",
        "天气" => "多云",
        "场景提示词" => "茂密的森林，阳光斑驳，充满神秘感",
        "分镜" => [
            [
                "分镜序号" => 3,
                "出境角色" => "小红帽",
                "字幕" => "小红帽走进了森林",
                "分镜提示词" => "小红帽在森林小径上行走，周围是高大的树木"
            ],
            [
                "分镜序号" => 4,
                "出境角色" => "小红帽, 森林精灵",
                "字幕" => "森林精灵出现了",
                "分镜提示词" => "发光的森林精灵从树后飞出，小红帽惊讶地看着"
            ]
        ]
    ]
];

echo "=== 测试项目创建功能 ===\n\n";

echo "1. 模拟AI返回的分镜剧本数据：\n";
echo json_encode($mockStoryboardData, JSON_UNESCAPED_UNICODE | JSON_PRETTY_PRINT) . "\n\n";

echo "2. 验证数据结构：\n";
echo "- 故事标题: " . $mockStoryboardData['故事标题'] . "\n";
echo "- 故事简概: " . $mockStoryboardData['故事简概'] . "\n";
echo "- 场景数量: " . count(array_filter(array_keys($mockStoryboardData), function($key) {
    return preg_match('/^场景\d+$/', $key);
})) . "\n";

$totalStoryboards = 0;
foreach ($mockStoryboardData as $key => $value) {
    if (preg_match('/^场景\d+$/', $key) && is_array($value) && isset($value['分镜'])) {
        $totalStoryboards += count($value['分镜']);
    }
}
echo "- 分镜总数: " . $totalStoryboards . "\n\n";

echo "3. 测试parseAiStoryboardJson方法的数据提取：\n";

// 验证故事标题和简概
if (isset($mockStoryboardData['故事标题']) && !empty($mockStoryboardData['故事标题'])) {
    echo "✅ 故事标题提取正常\n";
} else {
    echo "❌ 故事标题提取失败\n";
}

if (isset($mockStoryboardData['故事简概']) && !empty($mockStoryboardData['故事简概'])) {
    echo "✅ 故事简概提取正常\n";
} else {
    echo "❌ 故事简概提取失败\n";
}

// 验证场景数据
$sceneCount = 0;
$validScenes = 0;
foreach ($mockStoryboardData as $key => $value) {
    if (preg_match('/^场景\d+$/', $key) && is_array($value)) {
        $sceneCount++;
        
        $requiredFields = ['场景名称', '空间', '时间', '天气', '场景提示词', '分镜'];
        $hasAllFields = true;
        foreach ($requiredFields as $field) {
            if (!isset($value[$field]) || ($field !== '分镜' && empty($value[$field]))) {
                $hasAllFields = false;
                break;
            }
        }
        
        if ($hasAllFields && is_array($value['分镜']) && !empty($value['分镜'])) {
            $validScenes++;
            
            // 验证分镜数据
            foreach ($value['分镜'] as $storyboard) {
                $requiredStoryboardFields = ['分镜序号', '出境角色', '字幕', '分镜提示词'];
                foreach ($requiredStoryboardFields as $field) {
                    if (!isset($storyboard[$field]) || empty($storyboard[$field])) {
                        echo "⚠️  场景 {$key} 的分镜数据不完整，缺少字段: {$field}\n";
                    }
                }
            }
        }
    }
}

echo "✅ 场景数据验证: {$validScenes}/{$sceneCount} 个场景数据完整\n\n";

echo "4. 项目创建所需的数据：\n";
echo "- 用户ID: 1 (测试用户)\n";
echo "- 故事标题: " . $mockStoryboardData['故事标题'] . "\n";
echo "- 故事简概: " . $mockStoryboardData['故事简概'] . "\n";
echo "- 风格ID: null (可选)\n";
echo "- 故事内容: JSON格式的完整分镜剧本\n\n";

echo "5. 预期的创建流程：\n";
echo "1) ProjectStoryboardService::extractStoryboardsFromStory() 被调用\n";
echo "2) parseAiStoryboardJson() 解析分镜剧本数据\n";
echo "3) ProjectService::createFromStoryboard() 创建项目\n";
echo "4) batchCreateScenarios() 创建场景记录\n";
echo "5) batchCreateStoryboards() 创建分镜记录\n";
echo "6) 返回完整的创建结果\n\n";

echo "=== 测试完成 ===\n";
echo "新功能已实现，可以通过调用以下方法进行实际测试：\n";
echo "\$storyboardService = new ProjectStoryboardService();\n";
echo "\$result = \$storyboardService->extractStoryboardsFromStory(\$userId, json_encode(\$mockStoryboardData, JSON_UNESCAPED_UNICODE), \$styleId);\n";

?>
