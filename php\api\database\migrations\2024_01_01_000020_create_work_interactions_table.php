<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 创建作品互动表
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('work_interactions', function (Blueprint $table) {
            $table->id()->comment('主键ID');
            $table->unsignedBigInteger('work_id')->comment('作品ID，关联p_user_works表');
            $table->unsignedBigInteger('user_id')->comment('用户ID，关联p_users表');
            $table->enum('interaction_type', ['like', 'favorite', 'view', 'download', 'share'])->comment('互动类型');
            $table->json('metadata')->nullable()->comment('互动元数据');
            $table->string('ip_address', 45)->nullable()->comment('IP地址');
            $table->string('user_agent', 500)->nullable()->comment('用户代理');
            $table->timestamps();

            // 索引
            $table->index(['work_id', 'interaction_type'], 'idx_work_type');
            $table->index(['user_id', 'interaction_type'], 'idx_user_type');
            $table->index(['work_id', 'user_id'], 'idx_work_user');
            $table->index(['interaction_type', 'created_at'], 'idx_type_created');
            $table->index('created_at', 'idx_created');

            // 外键
            $table->foreign('work_id')->references('id')->on('user_works')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');

            // 唯一约束（防止重复互动）
            $table->unique(['work_id', 'user_id', 'interaction_type'], 'uk_work_user_interaction');

            $table->comment('作品互动表 - 记录用户对作品的各种互动行为');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('work_interactions');
    }
};
