<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 创建项目表
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('projects', function (Blueprint $table) {
            $table->id()->comment('主键ID');
            $table->string('name', 200)->comment('项目名称');
            $table->text('description')->nullable()->comment('项目描述');
            $table->string('project_type', 50)->comment('项目类型');
            $table->enum('status', ['draft', 'in_progress', 'completed', 'archived'])->default('draft')->comment('项目状态');
            $table->json('settings')->nullable()->comment('项目设置');
            $table->json('metadata')->nullable()->comment('项目元数据');
            $table->string('cover_image', 255)->nullable()->comment('封面图片');
            $table->json('tags')->nullable()->comment('标签集合');
            $table->unsignedBigInteger('owner_id')->comment('项目所有者ID，关联p_users表');
            $table->boolean('is_public')->default(false)->comment('是否公开');
            $table->boolean('is_template')->default(false)->comment('是否为模板');
            $table->integer('view_count')->default(0)->comment('查看次数');
            $table->integer('like_count')->default(0)->comment('点赞次数');
            $table->integer('share_count')->default(0)->comment('分享次数');
            $table->timestamp('last_accessed_at')->nullable()->comment('最后访问时间');
            $table->timestamps();

            // 索引
            $table->index(['owner_id', 'status'], 'idx_owner_status');
            $table->index(['project_type', 'status'], 'idx_type_status');
            $table->index(['is_public', 'status'], 'idx_public_status');
            $table->index(['is_template', 'is_public'], 'idx_template_public');
            $table->index(['view_count', 'is_public'], 'idx_view_public');
            $table->index(['like_count', 'is_public'], 'idx_like_public');
            $table->index(['last_accessed_at', 'owner_id'], 'idx_accessed_owner');
            $table->index('created_at', 'idx_created');

            // 外键
            $table->foreign('owner_id')->references('id')->on('users')->onDelete('cascade');

            $table->comment('项目表 - 管理用户创建的各种项目');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('projects');
    }
};
