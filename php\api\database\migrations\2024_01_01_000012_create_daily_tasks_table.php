<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 创建每日任务表
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('daily_tasks', function (Blueprint $table) {
            $table->id()->comment('主键ID');
            $table->string('name', 100)->comment('任务名称');
            $table->text('description')->comment('任务描述');
            $table->string('task_type', 50)->comment('任务类型');
            $table->json('conditions')->comment('完成条件');
            $table->json('rewards')->comment('奖励内容');
            $table->integer('points')->default(0)->comment('奖励积分');
            $table->integer('max_completions')->default(1)->comment('每日最大完成次数');
            $table->boolean('is_active')->default(true)->comment('是否激活');
            $table->integer('sort_order')->default(0)->comment('排序顺序');
            $table->date('valid_from')->nullable()->comment('有效开始日期');
            $table->date('valid_to')->nullable()->comment('有效结束日期');
            $table->timestamps();

            // 索引
            $table->index(['task_type', 'is_active'], 'idx_type_active');
            $table->index(['is_active', 'sort_order'], 'idx_active_sort');
            $table->index(['valid_from', 'valid_to'], 'idx_valid_period');

            $table->comment('每日任务表 - 定义系统每日任务');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('daily_tasks');
    }
};
