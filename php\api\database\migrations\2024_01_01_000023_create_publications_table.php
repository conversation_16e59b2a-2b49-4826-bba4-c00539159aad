<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 创建发布表
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('publications', function (Blueprint $table) {
            $table->id()->comment('主键ID');
            $table->unsignedBigInteger('work_id')->comment('作品ID，关联p_user_works表');
            $table->unsignedBigInteger('user_id')->comment('发布者ID，关联p_users表');
            $table->enum('platform', ['internal', 'youtube', 'bilibili', 'tiktok', 'instagram', 'other'])->comment('发布平台');
            $table->string('platform_id', 200)->nullable()->comment('平台作品ID');
            $table->string('platform_url', 500)->nullable()->comment('平台链接');
            $table->enum('status', ['pending', 'publishing', 'published', 'failed', 'removed'])->default('pending')->comment('发布状态');
            $table->json('publish_config')->nullable()->comment('发布配置');
            $table->json('platform_response')->nullable()->comment('平台响应数据');
            $table->text('error_message')->nullable()->comment('错误信息');
            $table->timestamp('published_at')->nullable()->comment('发布时间');
            $table->json('statistics')->nullable()->comment('发布统计数据');
            $table->timestamps();

            // 索引
            $table->index(['work_id', 'platform'], 'idx_work_platform');
            $table->index(['user_id', 'status'], 'idx_user_status');
            $table->index(['platform', 'status'], 'idx_platform_status');
            $table->index(['status', 'created_at'], 'idx_status_created');
            $table->index('published_at', 'idx_published');

            // 外键
            $table->foreign('work_id')->references('id')->on('user_works')->onDelete('cascade');
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');

            $table->comment('发布表 - 管理作品在各平台的发布状态');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('publications');
    }
};
