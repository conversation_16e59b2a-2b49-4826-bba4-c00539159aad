<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\StyleLibrary;

class StyleLibrarySeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        // 清理现有数据
        StyleLibrary::truncate();
        
        $styles = [
            // ========== 动画与漫画风格 (Animation & Comic Styles) ==========
            [
                'name' => '皮克斯风格',
                'description' => '皮克斯3D动画风格，可爱温馨的角色设计，适合家庭友好的内容',
                'category' => 'animation',
                'style_type' => 'ai_image',
                'style_config' => [
                    'art_style' => 'pixar_style',
                    'rendering' => '3d_animation',
                    'color_palette' => 'warm_vibrant',
                    'character_design' => 'cute_friendly',
                    'lighting' => 'soft_cinematic'
                ],
                'prompt_template' => 'Pixar style, 3D animation, cute characters, warm lighting, family-friendly, high quality render',
                'thumbnail' => 'https://example.com/styles/pixar_style_thumb.jpg',
                'sample_images' => [
                    'https://example.com/styles/pixar_sample1.jpg',
                    'https://example.com/styles/pixar_sample2.jpg'
                ],
                'is_active' => true,
                'is_premium' => false,
                'is_featured' => true,
                'sort_order' => 100,
                'usage_count' => 0,
                'rating' => 4.8,
                'rating_count' => 0,
                'tags' => ['皮克斯', '3D动画', '可爱', '温馨', '家庭'],
                'created_by' => null
            ],
            [
                'name' => '迪士尼经典风格',
                'description' => '迪士尼经典手绘动画风格，传统2D动画的魅力',
                'category' => 'animation',
                'style_type' => 'ai_image',
                'style_config' => [
                    'art_style' => 'disney_classic',
                    'rendering' => '2d_hand_drawn',
                    'color_palette' => 'classic_disney',
                    'character_design' => 'traditional_disney',
                    'line_art' => 'clean_expressive'
                ],
                'prompt_template' => 'Disney classic style, hand-drawn animation, traditional 2D, expressive characters, classic Disney colors',
                'thumbnail' => 'https://example.com/styles/disney_classic_thumb.jpg',
                'sample_images' => [
                    'https://example.com/styles/disney_classic_sample1.jpg',
                    'https://example.com/styles/disney_classic_sample2.jpg'
                ],
                'is_active' => true,
                'is_premium' => false,
                'is_featured' => false,
                'sort_order' => 95,
                'usage_count' => 0,
                'rating' => 4.7,
                'rating_count' => 0,
                'tags' => ['迪士尼', '经典', '手绘', '2D动画', '传统'],
                'created_by' => null
            ],
            [
                'name' => '迪士尼现代风格',
                'description' => '迪士尼现代3D动画风格，如《冰雪奇缘》的精美3D效果',
                'category' => 'animation',
                'style_type' => 'ai_image',
                'style_config' => [
                    'art_style' => 'disney_modern',
                    'rendering' => '3d_cgi',
                    'color_palette' => 'modern_disney',
                    'character_design' => 'stylized_realistic',
                    'lighting' => 'cinematic_advanced'
                ],
                'prompt_template' => 'Disney modern style, 3D CGI animation, Frozen style, beautiful lighting, detailed textures, high quality',
                'thumbnail' => 'https://example.com/styles/disney_modern_thumb.jpg',
                'sample_images' => [
                    'https://example.com/styles/disney_modern_sample1.jpg',
                    'https://example.com/styles/disney_modern_sample2.jpg'
                ],
                'is_active' => true,
                'is_premium' => true,
                'is_featured' => true,
                'sort_order' => 90,
                'usage_count' => 0,
                'rating' => 4.9,
                'rating_count' => 0,
                'tags' => ['迪士尼', '现代', '3D', 'CGI', '冰雪奇缘'],
                'created_by' => null
            ],
            [
                'name' => '日式动漫风格',
                'description' => '日本动漫风格，精美的2D角色设计和背景',
                'category' => 'animation',
                'style_type' => 'ai_image',
                'style_config' => [
                    'art_style' => 'anime_style',
                    'rendering' => '2d_anime',
                    'color_palette' => 'anime_vibrant',
                    'character_design' => 'anime_detailed',
                    'background' => 'detailed_scenic'
                ],
                'prompt_template' => 'Anime style, Japanese animation, detailed characters, vibrant colors, beautiful backgrounds, high quality 2D',
                'thumbnail' => 'https://example.com/styles/anime_style_thumb.jpg',
                'sample_images' => [
                    'https://example.com/styles/anime_style_sample1.jpg',
                    'https://example.com/styles/anime_style_sample2.jpg'
                ],
                'is_active' => true,
                'is_premium' => false,
                'is_featured' => false,
                'sort_order' => 85,
                'usage_count' => 0,
                'rating' => 4.6,
                'rating_count' => 0,
                'tags' => ['动漫', '日式', '2D', '精美', '角色'],
                'created_by' => null
            ],
            [
                'name' => '宫崎骏风格',
                'description' => '宫崎骏吉卜力工作室风格，温暖治愈的手绘动画',
                'category' => 'animation',
                'style_type' => 'ai_image',
                'style_config' => [
                    'art_style' => 'ghibli_style',
                    'rendering' => 'hand_drawn_detailed',
                    'color_palette' => 'natural_warm',
                    'character_design' => 'gentle_expressive',
                    'atmosphere' => 'peaceful_magical'
                ],
                'prompt_template' => 'Studio Ghibli style, Miyazaki style, hand-drawn animation, natural colors, peaceful atmosphere, detailed backgrounds',
                'thumbnail' => 'https://example.com/styles/ghibli_style_thumb.jpg',
                'sample_images' => [
                    'https://example.com/styles/ghibli_style_sample1.jpg',
                    'https://example.com/styles/ghibli_style_sample2.jpg'
                ],
                'is_active' => true,
                'is_premium' => true,
                'is_featured' => true,
                'sort_order' => 80,
                'usage_count' => 0,
                'rating' => 4.9,
                'rating_count' => 0,
                'tags' => ['宫崎骏', '吉卜力', '治愈', '手绘', '自然'],
                'created_by' => null
            ]
        ];

        foreach ($styles as $style) {
            StyleLibrary::create($style);
        }
    }
}
