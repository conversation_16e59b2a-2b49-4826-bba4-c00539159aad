<?php

namespace App\Models;

use Illuminate\Auth\Authenticatable;
use Illuminate\Contracts\Auth\Access\Authorizable as AuthorizableContract;
use Illuminate\Contracts\Auth\Authenticatable as AuthenticatableContract;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\HasMany;
use Laravel\Lumen\Auth\Authorizable;
/**
 * 用户模型
 * 表名会自动添加p_前缀，实际表名为p_users
 * 注意：模型中只设置基础表名，框架会自动添加前缀
 */
class User extends Model implements AuthenticatableContract, AuthorizableContract
{
    use Authenticatable, Authorizable, HasFactory;

    /**
     * 表名（框架会自动添加p_前缀）
     */
    protected $table = 'users';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'username',
        'email',
        'password',  // 数据库中实际字段名
        'nickname',
        'avatar',
        'bio',
        'level',
        'experience',  // 添加经验值字段
        'follower_count',
        'following_count',
        'inviter_id',
        'remark',
        'status',
        'points',
        'frozen_points',
        'is_vip',
        'vip_expires_at',
        'last_login_ip',
        'last_login_at'
    ];

    /**
     * 隐藏的属性
     */
    protected $hidden = [
        'password',  // 数据库中实际字段名
        'remember_token',
    ];

    /**
     * 获取认证密码字段名
     */
    public function getAuthPassword()
    {
        return $this->password;
    }

    /**
     * 获取认证标识符字段名
     */
    public function getAuthIdentifierName()
    {
        return 'username';
    }

    /**
     * 属性类型转换
     */
    protected $casts = [
        'level' => 'integer',
        'experience' => 'integer',
        'follower_count' => 'integer',
        'following_count' => 'integer',
        'status' => 'integer',
        'points' => 'decimal:2',
        'frozen_points' => 'decimal:2',
        'is_vip' => 'boolean',
        'vip_expires_at' => 'datetime',
        'last_login_at' => 'datetime',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 状态常量
     */
    const STATUS_DISABLED = 0;
    const STATUS_ENABLED = 1;

    /**
     * 关联积分交易记录
     */
    public function pointsTransactions(): HasMany
    {
        return $this->hasMany(PointsTransaction::class);
    }

    /**
     * 关联积分冻结记录
     */
    public function pointsFreeze(): HasMany
    {
        return $this->hasMany(PointsFreeze::class);
    }

    /**
     * 关联用户偏好
     */
    public function preference(): \Illuminate\Database\Eloquent\Relations\HasOne
    {
        return $this->hasOne(UserPreference::class);
    }

    /**
     * 关联项目
     */
    public function projects(): HasMany
    {
        return $this->hasMany(Project::class);
    }

    /**
     * 关联创建的风格
     */
    public function createdStyles(): HasMany
    {
        return $this->hasMany(StyleLibrary::class, 'created_by');
    }

    /**
     * 关联用户资源
     */
    public function resources(): HasMany
    {
        return $this->hasMany(Resource::class);
    }

    /**
     * 关联用户模型偏好
     */
    public function modelPreferences(): HasMany
    {
        return $this->hasMany(UserModelPreference::class);
    }

    /**
     * 关联平台使用统计
     */
    public function platformUsageStatistics(): HasMany
    {
        return $this->hasMany(PlatformUsageStatistic::class);
    }

    /**
     * 获取特定业务类型的模型偏好
     */
    public function getModelPreference(string $businessType): ?UserModelPreference
    {
        return $this->modelPreferences()->where('business_type', $businessType)->first();
    }

    /**
     * 获取特定任务类型和平台的使用统计
     */
    public function getPlatformUsageStatistic(string $taskType, string $platform): ?PlatformUsageStatistic
    {
        return $this->platformUsageStatistics()
                    ->where('task_type', $taskType)
                    ->where('platform', $platform)
                    ->first();
    }

    /**
     * 检查是否有足够积分
     */
    public function hasEnoughPoints(float $amount): bool
    {
        return $this->points >= $amount;
    }

    /**
     * 冻结积分
     */
    public function freezePoints(float $amount, string $businessType, string $businessId = null): bool
    {
        if (!$this->hasEnoughPoints($amount)) {
            return false;
        }

        $this->points -= $amount;
        $this->frozen_points += $amount;

        return $this->save();
    }

    /**
     * 释放冻结积分
     */
    public function releasePoints(float $amount): bool
    {
        if ($this->frozen_points < $amount) {
            return false;
        }

        $this->frozen_points -= $amount;
        $this->points += $amount;

        return $this->save();
    }

    /**
     * 消费冻结积分
     */
    public function consumeFrozenPoints(float $amount): bool
    {
        if ($this->frozen_points < $amount) {
            return false;
        }

        $this->frozen_points -= $amount;

        return $this->save();
    }
}
