<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 创建角色分类表
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('character_categories', function (Blueprint $table) {
            $table->id()->comment('主键ID');
            $table->string('name', 100)->comment('分类名称');
            $table->text('description')->nullable()->comment('分类描述');
            $table->string('icon', 255)->nullable()->comment('分类图标');
            $table->string('color', 20)->nullable()->comment('分类颜色');
            $table->unsignedBigInteger('parent_id')->nullable()->comment('父分类ID，关联本表');
            $table->boolean('is_active')->default(true)->comment('是否激活');
            $table->integer('sort_order')->default(0)->comment('排序顺序');
            $table->integer('character_count')->default(0)->comment('角色数量');
            $table->timestamps();

            // 索引
            $table->index(['parent_id', 'is_active'], 'idx_parent_active');
            $table->index(['is_active', 'sort_order'], 'idx_active_sort');
            $table->index('character_count', 'idx_character_count');

            // 外键
            $table->foreign('parent_id')->references('id')->on('character_categories')->onDelete('set null');

            $table->comment('角色分类表 - 管理角色库的分类体系');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('character_categories');
    }
};
