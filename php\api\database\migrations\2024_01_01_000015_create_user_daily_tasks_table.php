<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 创建用户每日任务表
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_daily_tasks', function (Blueprint $table) {
            $table->id()->comment('主键ID');
            $table->unsignedBigInteger('user_id')->comment('用户ID，关联p_users表');
            $table->unsignedBigInteger('task_id')->comment('任务ID，关联p_daily_tasks表');
            $table->date('task_date')->comment('任务日期');
            $table->integer('progress')->default(0)->comment('完成进度');
            $table->integer('completions')->default(0)->comment('完成次数');
            $table->boolean('is_completed')->default(false)->comment('是否完成');
            $table->timestamp('completed_at')->nullable()->comment('完成时间');
            $table->json('completion_data')->nullable()->comment('完成数据');
            $table->timestamps();

            // 索引
            $table->index(['user_id', 'task_date'], 'idx_user_date');
            $table->index(['task_id', 'task_date'], 'idx_task_date');
            $table->index(['user_id', 'is_completed'], 'idx_user_completed');
            $table->index(['task_date', 'is_completed'], 'idx_date_completed');

            // 外键
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('task_id')->references('id')->on('daily_tasks')->onDelete('cascade');

            // 唯一约束
            $table->unique(['user_id', 'task_id', 'task_date'], 'uk_user_task_date');

            $table->comment('用户每日任务表 - 记录用户每日任务的完成情况');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_daily_tasks');
    }
};
