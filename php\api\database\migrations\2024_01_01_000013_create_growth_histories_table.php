<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 创建成长历史表
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('growth_histories', function (Blueprint $table) {
            $table->id()->comment('主键ID');
            $table->unsignedBigInteger('user_id')->comment('用户ID，关联p_users表');
            $table->string('event_type', 50)->comment('事件类型');
            $table->string('event_description')->comment('事件描述');
            $table->integer('points_change')->default(0)->comment('积分变化');
            $table->integer('level_before')->comment('变化前等级');
            $table->integer('level_after')->comment('变化后等级');
            $table->integer('experience_before')->comment('变化前经验值');
            $table->integer('experience_after')->comment('变化后经验值');
            $table->json('metadata')->nullable()->comment('事件元数据');
            $table->timestamps();

            // 索引
            $table->index(['user_id', 'created_at'], 'idx_user_created');
            $table->index('event_type', 'idx_event_type');

            // 外键
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');

            $table->comment('用户成长历史表 - 记录用户等级和经验变化历史');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('growth_histories');
    }
};
