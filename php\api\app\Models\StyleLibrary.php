<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * 剧情风格库模型
 * 
 * @property int $id
 * @property string $name
 * @property string $description
 * @property string $category
 * @property array $style_config
 * @property string $prompt_template
 * @property string $thumbnail
 * @property bool $is_active
 * @property bool $is_premium
 * @property int $sort_order
 * @property int $usage_count
 * @property float $rating
 * @property array $tags
 * @property int $created_by
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class StyleLibrary extends Model
{
    /**
     * 表名（框架会自动添加p_前缀）
     */
    protected $table = 'style_library';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'name',
        'description',
        'category',
        'style_config',
        'prompt_template',
        'thumbnail',
        'is_active',
        'is_premium',
        'sort_order',
        'usage_count',
        'rating',
        'tags',
        'created_by'
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'style_config' => 'array',
        'is_active' => 'boolean',
        'is_premium' => 'boolean',
        'sort_order' => 'integer',
        'usage_count' => 'integer',
        'rating' => 'decimal:2',
        'tags' => 'array',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 默认值
     */
    protected $attributes = [
        'category' => 'general',
        'is_active' => true,
        'is_premium' => false,
        'sort_order' => 0,
        'usage_count' => 0,
        'rating' => 0
    ];

    /**
     * 分类常量
     */
    // 传统故事分类
    const CATEGORY_GENERAL = 'general';
    const CATEGORY_ROMANCE = 'romance';
    const CATEGORY_ADVENTURE = 'adventure';
    const CATEGORY_FANTASY = 'fantasy';
    const CATEGORY_SCIFI = 'scifi';
    const CATEGORY_HORROR = 'horror';
    const CATEGORY_COMEDY = 'comedy';
    const CATEGORY_DRAMA = 'drama';

    // AI生图风格分类
    const CATEGORY_ANIMATION = 'animation';        // 动画与漫画风格
    const CATEGORY_ART_MOVEMENT = 'art_movement';  // 艺术流派
    const CATEGORY_PAINTING = 'painting';          // 绘画媒介
    const CATEGORY_STEAMPUNK = 'steampunk';        // 蒸汽朋克
    const CATEGORY_REALISM = 'realism';            // 写实主义
    const CATEGORY_DIGITAL = 'digital';            // 数字艺术

    /**
     * 关联创建者
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 关联项目
     */
    public function projects(): HasMany
    {
        return $this->hasMany(Project::class, 'style_id');
    }

    /**
     * 增加使用次数
     */
    public function incrementUsage(): void
    {
        $this->increment('usage_count');
    }

    /**
     * 更新评分
     */
    public function updateRating(float $newRating): void
    {
        $this->rating = $newRating;
        $this->save();
    }

    /**
     * 获取风格配置
     */
    public function getStyleConfig(string $key, $default = null)
    {
        return data_get($this->style_config, $key, $default);
    }

    /**
     * 设置风格配置
     */
    public function setStyleConfig(string $key, $value): void
    {
        $config = $this->style_config ?? [];
        data_set($config, $key, $value);
        $this->style_config = $config;
    }

    /**
     * 检查是否包含标签
     */
    public function hasTag(string $tag): bool
    {
        return in_array($tag, $this->tags ?? []);
    }

    /**
     * 添加标签
     */
    public function addTag(string $tag): void
    {
        $tags = $this->tags ?? [];
        if (!in_array($tag, $tags)) {
            $tags[] = $tag;
            $this->tags = $tags;
        }
    }

    /**
     * 移除标签
     */
    public function removeTag(string $tag): void
    {
        $tags = $this->tags ?? [];
        $this->tags = array_values(array_filter($tags, fn($t) => $t !== $tag));
    }

    /**
     * 作用域：活跃的风格
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 作用域：按分类筛选
     */
    public function scopeByCategory($query, $category)
    {
        return $query->where('category', $category);
    }

    /**
     * 作用域：高级风格
     */
    public function scopePremium($query)
    {
        return $query->where('is_premium', true);
    }

    /**
     * 作用域：免费风格
     */
    public function scopeFree($query)
    {
        return $query->where('is_premium', false);
    }

    /**
     * 作用域：按排序权重排序
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'desc')->orderBy('usage_count', 'desc');
    }

    /**
     * 作用域：热门风格
     */
    public function scopePopular($query, $limit = 10)
    {
        return $query->orderBy('usage_count', 'desc')->limit($limit);
    }

    /**
     * 作用域：高评分风格
     */
    public function scopeHighRated($query, $minRating = 4.0)
    {
        return $query->where('rating', '>=', $minRating)->orderBy('rating', 'desc');
    }
}
