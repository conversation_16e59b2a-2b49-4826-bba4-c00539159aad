<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

/**
 * 角色库模型
 * 
 * @property int $id
 * @property string $name
 * @property string $description
 * @property int $category_id
 * @property string $gender
 * @property string $age_range
 * @property string $personality
 * @property string $background
 * @property string $appearance
 * @property string $avatar
 * @property array $images
 * @property array $voice_config
 * @property array $style_preferences
 * @property array $tags
 * @property bool $is_active
 * @property bool $is_premium
 * @property bool $is_featured
 * @property int $sort_order
 * @property int $binding_count
 * @property float $rating
 * @property int $rating_count
 * @property int $created_by
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class CharacterLibrary extends Model
{
    /**
     * 表名（框架会自动添加p_前缀）
     */
    protected $table = 'character_library';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'name',
        'description',
        'category_id',
        'gender',
        'age_range',
        'personality',
        'background',
        'appearance',
        'avatar',
        'images',
        'voice_config',
        'style_preferences',
        'tags',
        'is_active',
        'is_premium',
        'is_featured',
        'sort_order',
        'binding_count',
        'rating',
        'rating_count',
        'created_by'
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'images' => 'array',
        'voice_config' => 'array',
        'style_preferences' => 'array',
        'tags' => 'array',
        'is_active' => 'boolean',
        'is_premium' => 'boolean',
        'is_featured' => 'boolean',
        'sort_order' => 'integer',
        'binding_count' => 'integer',
        'rating' => 'decimal:2',
        'rating_count' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 默认值
     */
    protected $attributes = [
        'is_active' => true,
        'is_premium' => false,
        'is_featured' => false,
        'sort_order' => 0,
        'binding_count' => 0,
        'rating' => 0,
        'rating_count' => 0
    ];

    /**
     * 性别常量
     */
    const GENDER_MALE = 'male';
    const GENDER_FEMALE = 'female';
    const GENDER_OTHER = 'other';

    /**
     * 关联分类
     */
    public function category(): BelongsTo
    {
        return $this->belongsTo(CharacterCategory::class, 'category_id');
    }

    /**
     * 关联创建者
     */
    public function creator(): BelongsTo
    {
        return $this->belongsTo(User::class, 'created_by');
    }

    /**
     * 关联用户绑定
     */
    public function userBindings(): HasMany
    {
        return $this->hasMany(UserCharacterBinding::class, 'character_id');
    }

    /**
     * 增加绑定次数
     */
    public function incrementBinding(): void
    {
        $this->increment('binding_count');
    }

    /**
     * 减少绑定次数
     */
    public function decrementBinding(): void
    {
        $this->decrement('binding_count');
    }

    /**
     * 更新评分
     */
    public function updateRating(float $newRating): void
    {
        $totalRating = ($this->rating * $this->rating_count) + $newRating;
        $this->rating_count++;
        $this->rating = $totalRating / $this->rating_count;
        $this->save();
    }

    /**
     * 检查是否包含标签
     */
    public function hasTag(string $tag): bool
    {
        return in_array($tag, $this->tags ?? []);
    }

    /**
     * 添加标签
     */
    public function addTag(string $tag): void
    {
        $tags = $this->tags ?? [];
        if (!in_array($tag, $tags)) {
            $tags[] = $tag;
            $this->tags = $tags;
        }
    }

    /**
     * 移除标签
     */
    public function removeTag(string $tag): void
    {
        $tags = $this->tags ?? [];
        $this->tags = array_values(array_filter($tags, fn($t) => $t !== $tag));
    }

    /**
     * 获取语音配置
     */
    public function getVoiceConfig(string $key, $default = null)
    {
        return data_get($this->voice_config, $key, $default);
    }

    /**
     * 设置语音配置
     */
    public function setVoiceConfig(string $key, $value): void
    {
        $config = $this->voice_config ?? [];
        data_set($config, $key, $value);
        $this->voice_config = $config;
    }

    /**
     * 获取风格偏好
     */
    public function getStylePreference(string $key, $default = null)
    {
        return data_get($this->style_preferences, $key, $default);
    }

    /**
     * 设置风格偏好
     */
    public function setStylePreference(string $key, $value): void
    {
        $preferences = $this->style_preferences ?? [];
        data_set($preferences, $key, $value);
        $this->style_preferences = $preferences;
    }

    /**
     * 获取完整角色信息
     */
    public function getFullProfile(): array
    {
        return [
            'basic_info' => [
                'name' => $this->name,
                'description' => $this->description,
                'gender' => $this->gender,
                'age_range' => $this->age_range
            ],
            'personality' => $this->personality,
            'background' => $this->background,
            'appearance' => $this->appearance,
            'voice_config' => $this->voice_config ?? [],
            'style_preferences' => $this->style_preferences ?? [],
            'tags' => $this->tags ?? []
        ];
    }

    /**
     * 作用域：活跃的角色
     */
    public function scopeActive($query)
    {
        return $query->where('is_active', true);
    }

    /**
     * 作用域：按分类筛选
     */
    public function scopeByCategory($query, $categoryId)
    {
        return $query->where('category_id', $categoryId);
    }

    /**
     * 作用域：按性别筛选
     */
    public function scopeByGender($query, $gender)
    {
        return $query->where('gender', $gender);
    }

    /**
     * 作用域：高级角色
     */
    public function scopePremium($query)
    {
        return $query->where('is_premium', true);
    }

    /**
     * 作用域：免费角色
     */
    public function scopeFree($query)
    {
        return $query->where('is_premium', false);
    }

    /**
     * 作用域：推荐角色
     */
    public function scopeFeatured($query)
    {
        return $query->where('is_featured', true);
    }

    /**
     * 作用域：按排序权重排序
     */
    public function scopeOrdered($query)
    {
        return $query->orderBy('sort_order', 'desc')
            ->orderBy('binding_count', 'desc')
            ->orderBy('rating', 'desc');
    }

    /**
     * 作用域：热门角色
     */
    public function scopePopular($query, $limit = 10)
    {
        return $query->orderBy('binding_count', 'desc')->limit($limit);
    }

    /**
     * 作用域：高评分角色
     */
    public function scopeHighRated($query, $minRating = 4.0)
    {
        return $query->where('rating', '>=', $minRating)
            ->where('rating_count', '>=', 5)
            ->orderBy('rating', 'desc');
    }

    /**
     * 作用域：按标签筛选
     */
    public function scopeByTag($query, $tag)
    {
        return $query->whereJsonContains('tags', $tag);
    }

    /**
     * 作用域：搜索
     */
    public function scopeSearch($query, $keyword)
    {
        return $query->where(function($q) use ($keyword) {
            $q->where('name', 'like', "%{$keyword}%")
              ->orWhere('description', 'like', "%{$keyword}%")
              ->orWhere('personality', 'like', "%{$keyword}%")
              ->orWhereJsonContains('tags', $keyword);
        });
    }
}
