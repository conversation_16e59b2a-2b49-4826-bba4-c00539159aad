<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 创建用户成就表
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_achievements', function (Blueprint $table) {
            $table->id()->comment('主键ID');
            $table->unsignedBigInteger('user_id')->comment('用户ID，关联p_users表');
            $table->unsignedBigInteger('achievement_id')->comment('成就ID，关联p_achievements表');
            $table->integer('progress')->default(0)->comment('完成进度');
            $table->boolean('is_completed')->default(false)->comment('是否完成');
            $table->timestamp('completed_at')->nullable()->comment('完成时间');
            $table->json('completion_data')->nullable()->comment('完成数据');
            $table->timestamps();

            // 索引
            $table->index(['user_id', 'is_completed'], 'idx_user_completed');
            $table->index(['achievement_id', 'is_completed'], 'idx_achievement_completed');
            $table->index(['user_id', 'completed_at'], 'idx_user_completed_at');

            // 外键
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('achievement_id')->references('id')->on('achievements')->onDelete('cascade');

            // 唯一约束
            $table->unique(['user_id', 'achievement_id'], 'uk_user_achievement');

            $table->comment('用户成就表 - 记录用户获得的成就');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_achievements');
    }
};
