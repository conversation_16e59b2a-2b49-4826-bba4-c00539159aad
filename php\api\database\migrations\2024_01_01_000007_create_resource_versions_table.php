<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 创建资源版本表
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('resource_versions', function (Blueprint $table) {
            $table->id()->comment('主键ID');
            $table->unsignedBigInteger('resource_id')->comment('资源ID，关联p_resources表');
            $table->string('version', 50)->comment('版本号');
            $table->text('description')->nullable()->comment('版本描述');
            $table->string('file_path', 500)->nullable()->comment('文件路径');
            $table->string('file_url', 500)->nullable()->comment('文件URL');
            $table->bigInteger('file_size')->default(0)->comment('文件大小（字节）');
            $table->string('mime_type', 100)->nullable()->comment('MIME类型');
            $table->json('metadata')->nullable()->comment('版本元数据');
            $table->json('changes')->nullable()->comment('变更记录');
            $table->enum('status', ['pending', 'processing', 'completed', 'failed'])->default('pending')->comment('处理状态');
            $table->boolean('is_current')->default(false)->comment('是否当前版本');
            $table->unsignedBigInteger('created_by')->comment('创建者ID，关联p_users表');
            $table->timestamps();

            // 索引
            $table->index(['resource_id', 'version'], 'idx_resource_version');
            $table->index(['resource_id', 'is_current'], 'idx_resource_current');
            $table->index(['resource_id', 'created_at'], 'idx_resource_created');
            $table->index(['status', 'created_at'], 'idx_status_created');
            $table->index(['created_by', 'created_at'], 'idx_creator_created');

            // 外键
            $table->foreign('resource_id')->references('id')->on('resources')->onDelete('cascade');
            $table->foreign('created_by')->references('id')->on('users')->onDelete('cascade');

            // 唯一约束
            $table->unique(['resource_id', 'version'], 'uk_resource_version');

            $table->comment('资源版本表 - 管理资源的版本历史');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('resource_versions');
    }
};
