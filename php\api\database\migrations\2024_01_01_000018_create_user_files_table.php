<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 创建用户文件表
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_files', function (Blueprint $table) {
            $table->id()->comment('主键ID');
            $table->unsignedBigInteger('user_id')->comment('用户ID，关联p_users表');
            $table->string('filename', 255)->comment('文件名');
            $table->string('original_name', 255)->comment('原始文件名');
            $table->string('file_path', 500)->comment('文件路径');
            $table->string('file_url', 500)->nullable()->comment('文件URL');
            $table->enum('file_type', ['image', 'video', 'audio', 'document', 'other'])->comment('文件类型');
            $table->string('mime_type', 100)->comment('MIME类型');
            $table->bigInteger('file_size')->comment('文件大小（字节）');
            $table->string('storage_type', 50)->default('local')->comment('存储类型');
            $table->json('metadata')->nullable()->comment('文件元数据');
            $table->enum('status', ['uploading', 'completed', 'failed', 'deleted'])->default('uploading')->comment('文件状态');
            $table->string('business_type', 50)->nullable()->comment('业务类型');
            $table->string('business_id', 100)->nullable()->comment('业务ID');
            $table->boolean('is_public')->default(false)->comment('是否公开');
            $table->integer('download_count')->default(0)->comment('下载次数');
            $table->timestamp('last_accessed_at')->nullable()->comment('最后访问时间');
            $table->timestamps();

            // 索引
            $table->index(['user_id', 'file_type'], 'idx_user_type');
            $table->index(['user_id', 'status'], 'idx_user_status');
            $table->index(['business_type', 'business_id'], 'idx_business');
            $table->index(['file_type', 'status'], 'idx_type_status');
            $table->index(['is_public', 'status'], 'idx_public_status');
            $table->index('created_at', 'idx_created');

            // 外键
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');

            $table->comment('用户文件表 - 管理用户上传的各种文件');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_files');
    }
};
