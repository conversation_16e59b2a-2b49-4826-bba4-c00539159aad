<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('user_model_preferences', function (Blueprint $table) {
            $table->decimal('success_rate', 5, 4)->default(0.0000)->comment('成功率')->after('usage_count');
            $table->decimal('avg_response_time', 8, 4)->default(0.0000)->comment('平均响应时间(秒)')->after('success_rate');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('user_model_preferences', function (Blueprint $table) {
            $table->dropColumn(['success_rate', 'avg_response_time']);
        });
    }
};
