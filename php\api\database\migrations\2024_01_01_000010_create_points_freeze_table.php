<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 创建积分冻结表
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('points_freeze', function (Blueprint $table) {
            $table->id()->comment('主键ID');
            $table->string('freeze_id', 100)->unique()->comment('冻结唯一标识');
            $table->unsignedBigInteger('user_id')->comment('用户ID，关联p_users表');
            $table->decimal('amount', 10, 2)->comment('冻结金额');
            $table->string('reason', 200)->comment('冻结原因');
            $table->string('business_type', 50)->comment('业务类型');
            $table->string('business_id', 100)->nullable()->comment('业务ID');
            $table->enum('status', ['active', 'released', 'expired'])->default('active')->comment('冻结状态');
            $table->timestamp('expires_at')->nullable()->comment('过期时间');
            $table->timestamp('released_at')->nullable()->comment('释放时间');
            $table->unsignedBigInteger('released_by')->nullable()->comment('释放人ID，关联p_users表');
            $table->text('release_reason')->nullable()->comment('释放原因');
            $table->json('metadata')->nullable()->comment('冻结元数据');
            $table->timestamps();

            // 索引
            $table->index(['user_id', 'status'], 'idx_user_status');
            $table->index(['business_type', 'business_id'], 'idx_business');
            $table->index(['status', 'expires_at'], 'idx_status_expires');
            $table->index(['status', 'created_at'], 'idx_status_created');
            $table->index('freeze_id', 'idx_freeze_id');

            // 外键
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
            $table->foreign('released_by')->references('id')->on('users')->onDelete('set null');

            $table->comment('积分冻结表 - 管理积分的冻结和释放');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('points_freeze');
    }
};
