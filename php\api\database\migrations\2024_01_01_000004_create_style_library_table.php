<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 创建风格库表
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('style_library', function (Blueprint $table) {
            $table->id()->comment('主键ID');
            $table->string('name', 100)->comment('风格名称');
            $table->text('description')->nullable()->comment('风格描述');
            $table->string('category', 50)->comment('风格分类');
            $table->string('style_type', 50)->comment('风格类型');
            $table->json('style_config')->nullable()->comment('风格配置参数');
            $table->string('preview_image', 255)->nullable()->comment('预览图片');
            $table->json('sample_images')->nullable()->comment('示例图片集合');
            $table->json('tags')->nullable()->comment('标签集合');
            $table->boolean('is_active')->default(true)->comment('是否激活');
            $table->boolean('is_premium')->default(false)->comment('是否付费风格');
            $table->boolean('is_featured')->default(false)->comment('是否推荐风格');
            $table->integer('sort_order')->default(0)->comment('排序顺序');
            $table->integer('usage_count')->default(0)->comment('使用次数');
            $table->decimal('rating', 3, 2)->default(0.00)->comment('评分');
            $table->integer('rating_count')->default(0)->comment('评分次数');
            $table->unsignedBigInteger('created_by')->nullable()->comment('创建者ID，关联p_users表');
            $table->timestamps();

            // 索引
            $table->index(['category', 'is_active'], 'idx_category_active');
            $table->index(['style_type', 'is_active'], 'idx_type_active');
            $table->index(['is_featured', 'is_active'], 'idx_featured_active');
            $table->index(['is_premium', 'is_active'], 'idx_premium_active');
            $table->index(['usage_count', 'is_active'], 'idx_usage_active');
            $table->index(['rating', 'rating_count'], 'idx_rating');
            $table->index(['created_by', 'created_at'], 'idx_creator_created');

            // 外键
            $table->foreign('created_by')->references('id')->on('users')->onDelete('set null');

            $table->comment('风格库表 - 管理AI生成的各种风格模板');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('style_library');
    }
};
