<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

/**
 * 创建系统监控表
 */
return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('system_monitors', function (Blueprint $table) {
            $table->id()->comment('主键ID');
            $table->string('monitor_type', 50)->comment('监控类型');
            $table->string('metric_name', 100)->comment('指标名称');
            $table->decimal('metric_value', 15, 4)->comment('指标值');
            $table->string('unit', 20)->nullable()->comment('单位');
            $table->json('metadata')->nullable()->comment('监控元数据');
            $table->json('tags')->nullable()->comment('标签集合');
            $table->enum('status', ['normal', 'warning', 'critical'])->default('normal')->comment('状态');
            $table->decimal('threshold_warning', 15, 4)->nullable()->comment('警告阈值');
            $table->decimal('threshold_critical', 15, 4)->nullable()->comment('严重阈值');
            $table->string('source', 100)->comment('数据来源');
            $table->timestamp('collected_at')->comment('采集时间');
            $table->timestamps();

            // 索引
            $table->index(['monitor_type', 'collected_at'], 'idx_type_collected');
            $table->index(['metric_name', 'collected_at'], 'idx_metric_collected');
            $table->index(['status', 'collected_at'], 'idx_status_collected');
            $table->index(['source', 'collected_at'], 'idx_source_collected');
            $table->index('collected_at', 'idx_collected');

            $table->comment('系统监控表 - 记录系统各项监控指标');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('system_monitors');
    }
};
