# py-api.html WebSocket 功能测试指南

## 功能概述

现在 `py-api.html` 已经支持 WebSocket 相关接口的测试，包括：

1. **WebSocket 认证连接** (`/py-api/websocket/auth`)
2. **WebSocket 文本生成** (`/py-api/ai/text/generate-with-websocket`)

## 测试步骤

### 步骤1：建立 WebSocket 认证连接

1. 在 `py-api.html` 中找到 `WebSocketController` 
2. 点击 `WebSocket连接认证` 接口
3. 在 **在线测试** 标签页中：
   - 填写 `client_type`: `python_tool`
   - 填写 `business_type`: `text` (或 `story`, `prompt`, `storyboard` 等)
   - 确保 `Authorization` 头已设置
4. 点击 **建立认证连接** 按钮
5. 查看 WebSocket 面板中的状态变化和返回的会话信息

### 步骤2：连接到 WebSocket 服务器

1. 认证成功后，在 WebSocket 面板中点击 **连接 WebSocket** 按钮
2. 系统会自动使用返回的 `session_id` 连接到 WebSocket 服务器
3. 观察连接状态和消息日志

### 步骤3：测试文本生成接口

1. 找到 `AiGenerationController` 中的 `generate-with-websocket` 接口
2. 在 **在线测试** 标签页中：
   - 填写 `prompt`: 您的文本生成提示
   - 填写其他可选参数
3. 点击 **提交** 按钮
4. 系统会自动添加 `websocket_session_id` 参数
5. 在 WebSocket 面板中观察实时进度消息

## 新增功能特性

### WebSocket 面板功能

- **连接状态显示**: 实时显示连接状态（未连接/连接中/已连接）
- **连接管理按钮**: 建立认证连接、连接WebSocket、断开连接
- **消息日志**: 显示所有发送和接收的消息，带时间戳
- **消息类型标识**: 
  - 蓝色：发送的消息
  - 绿色：接收的消息  
  - 红色：错误消息

### 自动化功能

- **自动会话管理**: 自动保存和使用 WebSocket 会话信息
- **自动参数注入**: 为 WebSocket 接口自动添加 `websocket_session_id`
- **连接状态检查**: 调用 WebSocket 接口前自动检查连接状态

## 界面说明

### WebSocket 面板组件

```
┌─ WebSocket 连接管理 ─────────────────┐
│ 连接状态：[已连接]                    │
│                                     │
│ [建立认证连接] [连接WebSocket] [断开] │
│ [清空消息]                          │
│                                     │
│ ┌─ WebSocket 消息 ─────────────────┐ │
│ │ [10:30:15] 发送: POST /auth     │ │
│ │ [10:30:16] 接收: 认证成功        │ │
│ │ [10:30:20] 接收: 进度更新 50%    │ │
│ └─────────────────────────────────┘ │
└─────────────────────────────────────┘
```

## 技术实现

### 参考 test.html 的实现

- **Base64 编码/解码**: 复用 test.html 中的 Base64 工具函数
- **WebSocket 连接管理**: 参考传统模式，适配现代会话模式
- **消息处理**: 支持 JSON 格式的消息解析和显示

### 兼容性

- **现代会话模式**: 支持 `/py-api/websocket/auth` + `session_id` 模式
- **传统模式兼容**: 保留对传统 WebSocket 连接的支持
- **多连接管理**: 支持同时管理多个 WebSocket 连接

## 故障排除

### 常见问题

1. **认证失败**: 检查 Authorization 头是否正确设置
2. **连接失败**: 确认 WebSocket 服务器是否运行在正确端口
3. **消息接收异常**: 检查浏览器控制台的错误信息

### 调试技巧

- 打开浏览器开发者工具查看网络请求
- 观察 WebSocket 面板中的消息日志
- 检查控制台输出的详细错误信息

## 下一步改进

- [ ] 添加消息过滤和搜索功能
- [ ] 支持自定义 WebSocket 消息发送
- [ ] 添加连接重试机制
- [ ] 支持多业务类型的并发测试
