<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Model;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * AI生成任务模型
 * 
 * @property int $id
 * @property int $user_id
 * @property int $project_id
 * @property int $model_config_id
 * @property string $task_type
 * @property string $platform
 * @property string $model_name
 * @property string $status
 * @property array $input_data
 * @property array $output_data
 * @property array $generation_params
 * @property string $external_task_id
 * @property float $cost
 * @property int $tokens_used
 * @property int $processing_time_ms
 * @property \Carbon\Carbon $started_at
 * @property \Carbon\Carbon $completed_at
 * @property string $error_message
 * @property array $metadata
 * @property int $retry_count
 * @property int $max_retries
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class AiGenerationTask extends Model
{
    /**
     * 表名（框架会自动添加p_前缀）
     */
    protected $table = 'ai_generation_tasks';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'user_id',
        'project_id',
        'model_config_id',
        'task_type',
        'platform',
        'model_name',
        'status',
        'input_data',
        'output_data',
        'generation_params',
        'external_task_id',
        'cost',
        'tokens_used',
        'processing_time_ms',
        'started_at',
        'completed_at',
        'error_message',
        'metadata',
        'retry_count',
        'max_retries'
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'input_data' => 'array',
        'output_data' => 'array',
        'generation_params' => 'array',
        'cost' => 'decimal:4',
        'tokens_used' => 'integer',
        'processing_time_ms' => 'integer',
        'started_at' => 'datetime',
        'completed_at' => 'datetime',
        'metadata' => 'array',
        'retry_count' => 'integer',
        'max_retries' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 默认值
     */
    protected $attributes = [
        'status' => 'pending',
        'cost' => 0,
        'retry_count' => 0,
        'max_retries' => 3
    ];

    /**
     * 任务状态常量
     */
    const STATUS_PENDING = 'pending';
    const STATUS_PROCESSING = 'processing';
    const STATUS_COMPLETED = 'completed';
    const STATUS_FAILED = 'failed';
    const STATUS_CANCELLED = 'cancelled';
    const STATUS_TIMEOUT = 'timeout';

    /**
     * 任务类型常量
     */
    const TYPE_IMAGE_GENERATION = 'image_generation';
    const TYPE_IMAGE_GENERATION_CHARACTER = 'image_generation_character';
    const TYPE_IMAGE_GENERATION_STYLE = 'image_generation_style';
    const TYPE_VIDEO_GENERATION = 'video_generation';
    const TYPE_VOICE_SYNTHESIS = 'voice_synthesis';
    const TYPE_MUSIC_GENERATION = 'music_generation';
    const TYPE_SOUND_GENERATION = 'sound_generation';
    const TYPE_TEXT_GENERATION = 'text_generation';
    const TYPE_TEXT_GENERATION_PROMPT = 'text_generation_prompt';
    const TYPE_TEXT_GENERATION_STORY = 'text_generation_story';
    const TYPE_TEXT_GENERATION_STORYBOARD = 'text_generation_storyboard';
    const TYPE_CHARACTER_GENERATION = 'character_generation';

    /**
     * 关联用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 关联项目
     */
    public function project(): BelongsTo
    {
        return $this->belongsTo(Project::class);
    }

    /**
     * 关联AI模型配置
     */
    public function modelConfig(): BelongsTo
    {
        return $this->belongsTo(AiModelConfig::class, 'model_config_id');
    }

    /**
     * 开始任务
     */
    public function start(): void
    {
        $this->status = self::STATUS_PROCESSING;
        $this->started_at = Carbon::now();
        $this->save();
    }

    /**
     * 完成任务
     */
    public function complete(array $outputData, ?int $tokensUsed = null): void
    {
        $this->status = self::STATUS_COMPLETED;
        $this->output_data = $outputData;
        $this->tokens_used = $tokensUsed;
        $this->completed_at = Carbon::now();
        
        if ($this->started_at) {
            $this->processing_time_ms = $this->started_at->diffInMilliseconds(Carbon::now());
        }
        
        $this->save();
    }

    /**
     * 任务失败
     */
    public function fail(string $errorMessage): void
    {
        $this->status = self::STATUS_FAILED;
        $this->error_message = $errorMessage;
        $this->completed_at = Carbon::now();
        
        if ($this->started_at) {
            $this->processing_time_ms = $this->started_at->diffInMilliseconds(Carbon::now());
        }
        
        $this->save();
    }

    /**
     * 取消任务
     */
    public function cancel(): void
    {
        $this->status = self::STATUS_CANCELLED;
        $this->completed_at = Carbon::now();
        $this->save();
    }

    /**
     * 任务超时
     */
    public function timeout(): void
    {
        $this->status = self::STATUS_TIMEOUT;
        $this->error_message = '任务执行超时';
        $this->completed_at = Carbon::now();
        
        if ($this->started_at) {
            $this->processing_time_ms = $this->started_at->diffInMilliseconds(Carbon::now());
        }
        
        $this->save();
    }

    /**
     * 增加重试次数
     */
    public function incrementRetry(): bool
    {
        if ($this->retry_count >= $this->max_retries) {
            return false;
        }
        
        $this->retry_count++;
        $this->status = self::STATUS_PENDING;
        $this->error_message = null;
        $this->save();
        
        return true;
    }

    /**
     * 检查是否可以重试
     */
    public function canRetry(): bool
    {
        return $this->retry_count < $this->max_retries && 
               in_array($this->status, [self::STATUS_FAILED, self::STATUS_TIMEOUT]);
    }

    /**
     * 检查是否已完成
     */
    public function isCompleted(): bool
    {
        return in_array($this->status, [
            self::STATUS_COMPLETED,
            self::STATUS_FAILED,
            self::STATUS_CANCELLED,
            self::STATUS_TIMEOUT
        ]);
    }

    /**
     * 检查是否成功
     */
    public function isSuccessful(): bool
    {
        return $this->status === self::STATUS_COMPLETED;
    }

    /**
     * 获取输入数据
     */
    public function getInputData(string $key, $default = null)
    {
        return data_get($this->input_data, $key, $default);
    }

    /**
     * 获取输出数据
     */
    public function getOutputData(string $key, $default = null)
    {
        return data_get($this->output_data, $key, $default);
    }

    /**
     * 获取元数据
     */
    public function getMetadata(string $key, $default = null)
    {
        return data_get($this->metadata, $key, $default);
    }

    /**
     * 设置元数据
     */
    public function setMetadata(string $key, $value): void
    {
        $metadata = $this->metadata ?? [];
        data_set($metadata, $key, $value);
        $this->metadata = $metadata;
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 作用域：按任务类型筛选
     */
    public function scopeByType($query, $type)
    {
        return $query->where('task_type', $type);
    }

    /**
     * 作用域：按平台筛选
     */
    public function scopeByPlatform($query, $platform)
    {
        return $query->where('platform', $platform);
    }

    /**
     * 作用域：按用户筛选
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }

    /**
     * 作用域：待处理的任务
     */
    public function scopePending($query)
    {
        return $query->where('status', self::STATUS_PENDING);
    }

    /**
     * 作用域：处理中的任务
     */
    public function scopeProcessing($query)
    {
        return $query->where('status', self::STATUS_PROCESSING);
    }

    /**
     * 作用域：已完成的任务
     */
    public function scopeCompleted($query)
    {
        return $query->where('status', self::STATUS_COMPLETED);
    }

    /**
     * 作用域：失败的任务
     */
    public function scopeFailed($query)
    {
        return $query->where('status', self::STATUS_FAILED);
    }

    /**
     * 作用域：可重试的任务
     */
    public function scopeRetryable($query)
    {
        return $query->whereIn('status', [self::STATUS_FAILED, self::STATUS_TIMEOUT])
            ->whereRaw('retry_count < max_retries');
    }
}
