<?php
/**
 * 调试控制器解析问题
 */

// 设置错误报告
error_reporting(E_ALL);
ini_set('display_errors', 1);

echo "<h1>控制器解析调试</h1>";

// 设置基本路径
define('LARAVEL_START', microtime(true));

try {
    // 包含必要的文件
    require_once __DIR__ . '/php/api/vendor/autoload.php';
    
    // 创建应用实例
    $app = require_once __DIR__ . '/php/api/bootstrap/app.php';
    
    echo "<h2>1. 目录检查</h2>";
    
    // 检查PyApi目录
    $possibleDirs = [
        base_path('app/Http/Controllers/PyApi'),
        app()->basePath('app/Http/Controllers/PyApi'),
        '../app/Http/Controllers/PyApi',
        __DIR__ . '/php/api/app/Http/Controllers/PyApi'
    ];
    
    $dirPath = null;
    foreach ($possibleDirs as $dir) {
        $exists = is_dir($dir);
        echo "目录 {$dir}: " . ($exists ? '<span style="color: green;">存在</span>' : '<span style="color: red;">不存在</span>') . "<br>";
        if ($exists && !$dirPath) {
            $dirPath = $dir;
        }
    }
    
    if (!$dirPath) {
        echo "<span style='color: red;'>❌ 找不到PyApi控制器目录!</span><br>";
        exit;
    }
    
    echo "<h3>使用目录: {$dirPath}</h3>";
    
    // 扫描文件
    $files = scandir($dirPath);
    $phpFiles = array_filter($files, function($file) {
        return $file != '.' && $file != '..' && pathinfo($file, PATHINFO_EXTENSION) === 'php';
    });
    
    echo "<h2>2. 文件扫描结果</h2>";
    echo "总文件数: " . count($phpFiles) . "<br>";
    echo "PHP文件列表:<br>";
    foreach ($phpFiles as $file) {
        echo "- " . $file . "<br>";
    }
    
    echo "<h2>3. 控制器解析测试</h2>";
    
    // 创建控制器实例
    $controller = new \App\Http\Controllers\PyApiDocumentController();
    
    // 测试getControllerList方法
    $listResponse = $controller->getControllerList();
    $listData = $listResponse->getData(true);
    
    echo "<h3>getControllerList 结果:</h3>";
    echo "状态: " . ($listData['success'] ? '<span style="color: green;">成功</span>' : '<span style="color: red;">失败</span>') . "<br>";
    echo "解析的控制器数量: " . count($listData['data'] ?? []) . "<br>";
    echo "预期控制器数量: " . count($phpFiles) . "<br>";
    
    if (count($listData['data'] ?? []) < count($phpFiles)) {
        echo "<span style='color: red;'>⚠️ 解析的控制器数量少于实际文件数量!</span><br>";
    }
    
    echo "<h3>解析成功的控制器:</h3>";
    if (!empty($listData['data'])) {
        foreach ($listData['data'] as $ctrl) {
            echo "- " . ($ctrl['name'] ?? '未知') . ": " . ($ctrl['title'] ?? '无标题') . " (" . ($ctrl['api_count'] ?? 0) . " APIs)<br>";
        }
    }
    
    echo "<h3>解析失败的控制器:</h3>";
    $parsedFiles = array_map(function($ctrl) { return $ctrl['file'] ?? ''; }, $listData['data'] ?? []);
    $missingFiles = array_diff($phpFiles, $parsedFiles);
    
    if (empty($missingFiles)) {
        echo "<span style='color: green;'>✅ 所有控制器都解析成功!</span><br>";
    } else {
        echo "<span style='color: red;'>❌ 以下控制器解析失败:</span><br>";
        foreach ($missingFiles as $file) {
            echo "- " . $file . "<br>";
            
            // 检查具体的解析问题
            $filePath = $dirPath . '/' . $file;
            if (file_exists($filePath)) {
                $content = file_get_contents($filePath);
                
                // 检查类注释格式
                if (preg_match("/\/\*\*\s*\n\s*\*\s*([^\n]+)\s*\n\s*\*\s*([^\n]*)\s*\n\s*\*\/\s*class/", $content, $matches)) {
                    echo "  → 找到多行注释: " . trim($matches[1]) . "<br>";
                } elseif (preg_match("/\/\*\*\s*\n\s*\*\s*([^\n]+)\s*\n\s*\*\/\s*class/", $content, $matches)) {
                    echo "  → 找到单行注释: " . trim($matches[1]) . "<br>";
                } else {
                    echo "  → <span style='color: red;'>未找到符合格式的类注释</span><br>";
                    
                    // 显示文件开头内容用于调试
                    $lines = explode("\n", $content);
                    echo "  → 文件开头内容:<br>";
                    for ($i = 0; $i < min(10, count($lines)); $i++) {
                        echo "    " . ($i + 1) . ": " . htmlspecialchars($lines[$i]) . "<br>";
                    }
                }
                
                // 检查@ApiTitle数量
                preg_match_all("/\@ApiTitle[^\@^\*]+/", $content, $apiMatches);
                echo "  → @ApiTitle 数量: " . count($apiMatches[0]) . "<br>";
            } else {
                echo "  → <span style='color: red;'>文件不存在</span><br>";
            }
        }
    }
    
    echo "<h2>4. 具体控制器测试</h2>";
    
    // 测试几个具体的控制器
    $testControllers = ['AiGenerationController', 'AiTaskController', 'AuthController'];
    
    foreach ($testControllers as $controllerName) {
        echo "<h3>测试 {$controllerName}:</h3>";
        
        $response = $controller->getControllerData($controllerName);
        $data = $response->getData(true);
        $status = $response->getStatusCode();
        
        echo "状态码: " . $status . "<br>";
        if ($status === 200) {
            $apiCount = count($data['data']['apis'] ?? []);
            echo "<span style='color: green;'>✅ 解析成功: {$apiCount} APIs</span><br>";
            echo "控制器标题: " . ($data['data']['title'] ?? '无标题') . "<br>";
        } else {
            echo "<span style='color: red;'>❌ 解析失败: " . ($data['message'] ?? '未知错误') . "</span><br>";
        }
    }
    
} catch (\Exception $e) {
    echo "<span style='color: red;'>❌ 异常: " . $e->getMessage() . "</span><br>";
    echo "错误位置: " . $e->getFile() . ":" . $e->getLine() . "<br>";
    echo "<h3>错误堆栈:</h3>";
    echo "<pre>" . $e->getTraceAsString() . "</pre>";
}

echo "<hr>";
echo "<h2>总结</h2>";
echo "<p>如果发现控制器解析失败，可能的原因:</p>";
echo "<ol>";
echo "<li><strong>类注释格式不符合要求</strong>: 需要 /** 开头的多行注释</li>";
echo "<li><strong>文件路径问题</strong>: 大小写敏感的文件系统</li>";
echo "<li><strong>PHP语法错误</strong>: 导致文件无法解析</li>";
echo "<li><strong>缺少@ApiTitle注解</strong>: 导致API数量为0</li>";
echo "</ol>";

?>
