<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('user_model_preferences', function (Blueprint $table) {
            $table->id()->comment('主键ID');
            $table->unsignedBigInteger('user_id')->comment('用户ID');
            $table->string('business_type', 50)->comment('业务类型');
            $table->string('preferred_platform', 50)->comment('首选平台');
            $table->json('platform_priorities')->comment('平台优先级配置');
            $table->json('selection_criteria')->default('{}')->comment('选择标准');
            $table->boolean('auto_fallback')->default(true)->comment('是否自动降级');
            $table->boolean('cost_optimization')->default(false)->comment('是否成本优化');
            $table->json('custom_config')->nullable()->comment('自定义配置');
            $table->integer('usage_count')->default(0)->comment('使用次数');
            $table->timestamp('last_used_at')->nullable()->comment('最后使用时间');
            
            // 🔧 新增：软删除支持
            $table->softDeletes()->comment('软删除时间');
            $table->timestamps();

            // 索引和约束
            $table->unique(['user_id', 'business_type'], 'uk_user_business');
            $table->index('preferred_platform', 'idx_preferred_platform');
            $table->index('last_used_at', 'idx_last_used');
            $table->index('deleted_at', 'idx_deleted_at');
            
            // 外键约束
            $table->foreign('user_id')->references('id')->on('users')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('user_model_preferences');
    }
};
