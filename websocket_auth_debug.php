<?php
/**
 * WebSocket认证调试脚本
 * 用于测试WebSocket认证接口的问题
 */

// 测试参数
$apiUrl = 'https://api.tiptop.cn/py-api/websocket/auth';
$token = 'vVP8UApdmXoU9B2z1jMtNMbrV8bGiP76w0ocpm7a1mZyY';

// 测试数据
$postData = [
    'client_type' => 'python_tool',
    'business_type' => 'text'
];

// 设置请求头
$headers = [
    'Authorization: Bearer ' . $token,
    'Content-Type: application/json',
    'Accept: application/json'
];

// 初始化cURL
$ch = curl_init();

// 设置cURL选项
curl_setopt_array($ch, [
    CURLOPT_URL => $apiUrl,
    CURLOPT_POST => true,
    CURLOPT_POSTFIELDS => json_encode($postData),
    CURLOPT_HTTPHEADER => $headers,
    CURLOPT_RETURNTRANSFER => true,
    CURLOPT_SSL_VERIFYPEER => false,
    CURLOPT_SSL_VERIFYHOST => false,
    CURLOPT_TIMEOUT => 30,
    CURLOPT_VERBOSE => true
]);

echo "=== WebSocket认证调试测试 ===\n";
echo "API URL: $apiUrl\n";
echo "Token: " . substr($token, 0, 10) . "...\n";
echo "POST Data: " . json_encode($postData) . "\n";
echo "Headers: " . implode(', ', $headers) . "\n\n";

// 执行请求
$response = curl_exec($ch);
$httpCode = curl_getinfo($ch, CURLINFO_HTTP_CODE);
$error = curl_error($ch);

curl_close($ch);

echo "=== 响应结果 ===\n";
echo "HTTP Code: $httpCode\n";
echo "Response: $response\n";

if ($error) {
    echo "cURL Error: $error\n";
}

// 解析响应
if ($response) {
    $responseData = json_decode($response, true);
    if ($responseData) {
        echo "\n=== 解析后的响应 ===\n";
        print_r($responseData);
    }
}

echo "\n=== 测试完成 ===\n";
?>
