<?php

namespace App\Models;

use Carbon\Carbon;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

/**
 * WebSocket会话模型
 *
 * @property int $id
 * @property string $session_id
 * @property int $user_id
 * @property string $client_type
 * @property string $business_type
 * @property string $client_version
 * @property string $connection_ip
 * @property string $user_agent
 * @property string $status
 * @property array $connection_info
 * @property array $subscribed_events
 * @property \Carbon\Carbon $connected_at
 * @property \Carbon\Carbon $last_ping_at
 * @property \Carbon\Carbon $disconnected_at
 * @property int $message_count
 * @property string $disconnect_reason
 * @property \Carbon\Carbon $created_at
 * @property \Carbon\Carbon $updated_at
 */
class WebSocketSession extends Model
{
    /**
     * 表名（框架会自动添加p_前缀）
     */
    protected $table = 'websocket_sessions';

    /**
     * 可批量赋值的属性
     */
    protected $fillable = [
        'session_id',
        'user_id',
        'client_type',
        'business_type',
        'client_version',
        'connection_ip',
        'user_agent',
        'status',
        'connection_info',
        'subscribed_events',
        'connected_at',
        'last_ping_at',
        'disconnected_at',
        'message_count',
        'disconnect_reason'
    ];

    /**
     * 属性类型转换
     */
    protected $casts = [
        'connection_info' => 'array',
        'subscribed_events' => 'array',
        'connected_at' => 'datetime',
        'last_ping_at' => 'datetime',
        'disconnected_at' => 'datetime',
        'message_count' => 'integer',
        'created_at' => 'datetime',
        'updated_at' => 'datetime'
    ];

    /**
     * 默认值
     */
    protected $attributes = [
        'client_type' => 'unknown',
        'status' => 'connected',
        'message_count' => 0
    ];

    /**
     * 连接状态常量
     */
    const STATUS_CONNECTED = 'connected';
    const STATUS_DISCONNECTED = 'disconnected';
    const STATUS_TIMEOUT = 'timeout';

    /**
     * 客户端类型常量
     */
    const CLIENT_TYPE_PYTHON_TOOL = 'python_tool';
    const CLIENT_TYPE_WEB_BROWSER = 'web_browser';
    const CLIENT_TYPE_MOBILE_APP = 'mobile_app';
    const CLIENT_TYPE_UNKNOWN = 'unknown';

    /**
     * 业务类型常量
     */
    const BUSINESS_TYPE_TEXT_GENERATION = 'text_generation';
    const BUSINESS_TYPE_STORYBOARD_GENERATION = 'storyboard_generation';
    const BUSINESS_TYPE_PROJECT_CREATION = 'project_creation';
    const BUSINESS_TYPE_CHARACTER_CREATION = 'character_creation';
    const BUSINESS_TYPE_VOICE_AUDITION = 'voice_audition';

    /**
     * 事件类型常量
     */
    const EVENT_AI_GENERATION_PROGRESS = 'ai_generation_progress';
    const EVENT_AI_GENERATION_COMPLETED = 'ai_generation_completed';
    const EVENT_AI_GENERATION_FAILED = 'ai_generation_failed';
    const EVENT_POINTS_CHANGED = 'points_changed';

    /**
     * 关联用户
     */
    public function user(): BelongsTo
    {
        return $this->belongsTo(User::class);
    }

    /**
     * 更新心跳时间
     */
    public function updatePing(): void
    {
        $this->last_ping_at = Carbon::now();
        $this->save();
    }

    /**
     * 断开连接
     */
    public function disconnect(?string $reason = null): void
    {
        $this->status = self::STATUS_DISCONNECTED;
        $this->disconnected_at = Carbon::now();
        $this->disconnect_reason = $reason;
        $this->save();
    }

    /**
     * 标记为超时
     */
    public function markAsTimeout(): void
    {
        $this->status = self::STATUS_TIMEOUT;
        $this->disconnected_at = Carbon::now();
        $this->disconnect_reason = '连接超时';
        $this->save();
    }

    /**
     * 增加消息计数
     */
    public function incrementMessageCount(): void
    {
        $this->increment('message_count');
    }

    /**
     * 订阅事件
     */
    public function subscribeEvent(string $eventType): void
    {
        $events = $this->subscribed_events ?? [];
        if (!in_array($eventType, $events)) {
            $events[] = $eventType;
            $this->subscribed_events = $events;
            $this->save();
        }
    }

    /**
     * 取消订阅事件
     */
    public function unsubscribeEvent(string $eventType): void
    {
        $events = $this->subscribed_events ?? [];
        $this->subscribed_events = array_values(array_filter($events, fn($e) => $e !== $eventType));
        $this->save();
    }

    /**
     * 检查是否订阅了事件
     */
    public function isSubscribedTo(string $eventType): bool
    {
        return in_array($eventType, $this->subscribed_events ?? []);
    }

    /**
     * 检查是否为Py视频创作工具客户端
     */
    public function isPythonTool(): bool
    {
        return $this->client_type === self::CLIENT_TYPE_PYTHON_TOOL;
    }

    /**
     * 检查连接是否活跃
     */
    public function isActive(): bool
    {
        if ($this->status !== self::STATUS_CONNECTED) {
            return false;
        }

        // 如果超过5分钟没有心跳，认为连接不活跃
        if ($this->last_ping_at && $this->last_ping_at->diffInMinutes(Carbon::now()) > 5) {
            return false;
        }

        return true;
    }

    /**
     * 获取连接时长（秒）
     */
    public function getConnectionDuration(): int
    {
        $endTime = $this->disconnected_at ?? Carbon::now();
        return $this->connected_at->diffInSeconds($endTime);
    }

    /**
     * 获取连接信息
     */
    public function getConnectionInfo(string $key, $default = null)
    {
        return data_get($this->connection_info, $key, $default);
    }

    /**
     * 设置连接信息
     */
    public function setConnectionInfo(string $key, $value): void
    {
        $info = $this->connection_info ?? [];
        data_set($info, $key, $value);
        $this->connection_info = $info;
    }

    /**
     * 作用域：按状态筛选
     */
    public function scopeByStatus($query, $status)
    {
        return $query->where('status', $status);
    }

    /**
     * 作用域：按客户端类型筛选
     */
    public function scopeByClientType($query, $clientType)
    {
        return $query->where('client_type', $clientType);
    }

    /**
     * 作用域：按业务类型筛选
     */
    public function scopeByBusinessType($query, $businessType)
    {
        return $query->where('business_type', $businessType);
    }

    /**
     * 作用域：活跃连接
     */
    public function scopeActive($query)
    {
        return $query->where('status', self::STATUS_CONNECTED)
            ->where(function($q) {
                $q->whereNull('last_ping_at')
                  ->orWhere('last_ping_at', '>', Carbon::now()->subMinutes(5));
            });
    }

    /**
     * 作用域：Py视频创作工具连接
     */
    public function scopePythonTool($query)
    {
        return $query->where('client_type', self::CLIENT_TYPE_PYTHON_TOOL);
    }

    /**
     * 作用域：超时连接
     */
    public function scopeTimeout($query)
    {
        return $query->where('status', self::STATUS_CONNECTED)
            ->where('last_ping_at', '<', Carbon::now()->subMinutes(5));
    }

    /**
     * 作用域：按用户筛选
     */
    public function scopeByUser($query, $userId)
    {
        return $query->where('user_id', $userId);
    }
}
